/**
 * Unit tests for UIOrbitExtension
 */

import { UIOrbitExtension } from '../UIOrbitExtension';

// Mock dependencies
jest.mock('../ServiceRegistry');
jest.mock('../../services/ConfigurationService');
jest.mock('../../webview/ChatWebviewProvider');
jest.mock('../../webview/UIOrbitWebviewProvider');
jest.mock('../../services/CommandService');
jest.mock('../../utils/Logger');

import { ServiceRegistry } from '../ServiceRegistry';
import { ConfigurationService } from '../../services/ConfigurationService';
import { ChatWebviewProvider } from '../../webview/ChatWebviewProvider';
import { CommandService } from '../../services/CommandService';

describe('UIOrbitExtension', () => {
  let extension: UIOrbitExtension;
  let mockContext: any;
  let mockServiceRegistry: jest.Mocked<ServiceRegistry>;
  let mockConfigService: jest.Mocked<ConfigurationService>;
  let mockChatProvider: jest.Mocked<ChatWebviewProvider>;

  beforeEach(() => {
    // Create mock context
    mockContext = global.testUtils.createMockContext();

    // Setup mocks
    mockServiceRegistry = {
      register: jest.fn(),
      get: jest.fn((serviceName: string) => {
        switch (serviceName) {
          case 'configuration':
            return mockConfigService;
          case 'fileWatching':
            return { stopWatching: jest.fn(), dispose: jest.fn() };
          case 'command':
            return { openChat: jest.fn(), dispose: jest.fn() };
          default:
            return undefined;
        }
      }),
      dispose: jest.fn(),
    } as any;

    mockConfigService = {
      initialize: jest.fn(),
      reload: jest.fn(),
      dispose: jest.fn(),
      getConfiguration: jest.fn(),
      getOpenAIApiKey: jest.fn(() => 'mock-api-key'),
      getDefaultFramework: jest.fn(() => 'react'),
      getDefaultStyling: jest.fn(() => 'tailwind'),
      isDebugMode: jest.fn(() => false),
      isAccessibilityEnabled: jest.fn(() => true),
      isResponsiveDesignEnabled: jest.fn(() => true),
    } as any;

    mockChatProvider = {
      dispose: jest.fn(),
    } as any;

    // Mock constructors
    (ServiceRegistry as jest.MockedClass<typeof ServiceRegistry>).mockImplementation(() => mockServiceRegistry);
    (ConfigurationService as jest.MockedClass<typeof ConfigurationService>).mockImplementation(() => mockConfigService);
    (ChatWebviewProvider as jest.MockedClass<typeof ChatWebviewProvider>).mockImplementation(() => mockChatProvider);

    // Mock VS Code API
    const mockDisposable = global.testUtils.createMockDisposable();
    require('vscode').window.registerWebviewViewProvider = jest.fn(() => mockDisposable);
    require('vscode').commands.registerCommand = jest.fn(() => mockDisposable);
    require('vscode').workspace.onDidChangeConfiguration = jest.fn(() => mockDisposable);

    extension = new UIOrbitExtension(mockContext);
  });

  afterEach(async () => {
    // Ensure extension is properly deactivated
    try {
      await extension.deactivate();
    } catch (error) {
      // Ignore deactivation errors in cleanup
    }

    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  describe('Construction', () => {
    it('should create extension instance successfully', () => {
      expect(extension).toBeInstanceOf(UIOrbitExtension);
      expect(ServiceRegistry).toHaveBeenCalledTimes(1);
    });
  });

  describe('Activation', () => {
    it('should activate successfully', async () => {
      await extension.activate();

      expect(mockServiceRegistry.register).toHaveBeenCalledWith('configuration', expect.any(Object));
      expect(mockConfigService.initialize).toHaveBeenCalled();
      expect(ChatWebviewProvider).toHaveBeenCalledWith(mockContext.extensionUri, mockServiceRegistry);
      expect(require('vscode').window.registerWebviewViewProvider).toHaveBeenCalledWith(
        'uiorbit.chatView',
        mockChatProvider,
        { webviewOptions: { retainContextWhenHidden: true } }
      );
    });

    it('should register all commands during activation', async () => {
      await extension.activate();

      expect(require('vscode').commands.registerCommand).toHaveBeenCalledWith('uiorbit.openChat', expect.any(Function));
      expect(require('vscode').commands.registerCommand).toHaveBeenCalledWith('uiorbit.generateComponent', expect.any(Function));
      expect(require('vscode').commands.registerCommand).toHaveBeenCalledWith('uiorbit.analyzeCode', expect.any(Function));
    });

    it('should setup event listeners during activation', async () => {
      await extension.activate();

      expect(require('vscode').workspace.onDidChangeConfiguration).toHaveBeenCalled();
    });

    it('should handle activation errors', async () => {
      mockConfigService.initialize.mockRejectedValue(new Error('Initialization failed'));

      await expect(extension.activate()).rejects.toThrow('Initialization failed');
    });

    it('should handle missing configuration service', async () => {
      // Override the service registry to return undefined for configuration service
      mockServiceRegistry.get.mockImplementation((serviceName: string) => {
        switch (serviceName) {
          case 'configuration':
            return undefined;
          case 'fileWatching':
            return { stopWatching: jest.fn(), dispose: jest.fn() };
          case 'command':
            return { openChat: jest.fn(), dispose: jest.fn() };
          default:
            return undefined;
        }
      });

      // Should not throw, but might log an error
      await expect(extension.activate()).resolves.not.toThrow();
    });
  });

  describe('Deactivation', () => {
    it('should deactivate successfully', async () => {
      // First activate - the service registry mock is already set up in beforeEach
      await extension.activate();

      // Then deactivate
      await extension.deactivate();

      expect(mockServiceRegistry.dispose).toHaveBeenCalled();
    });

    it('should dispose all disposables during deactivation', async () => {
      const mockDisposable1 = global.testUtils.createMockDisposable();
      const mockDisposable2 = global.testUtils.createMockDisposable();

      require('vscode').window.registerWebviewViewProvider.mockReturnValue(mockDisposable1);
      require('vscode').commands.registerCommand.mockReturnValue(mockDisposable2);

      // The service registry mock is already set up in beforeEach
      await extension.activate();
      await extension.deactivate();

      expect(mockDisposable1.dispose).toHaveBeenCalled();
      expect(mockDisposable2.dispose).toHaveBeenCalled();
    });

    it('should handle deactivation errors gracefully', async () => {
      mockServiceRegistry.dispose.mockRejectedValue(new Error('Disposal failed'));

      await expect(extension.deactivate()).rejects.toThrow('Disposal failed');
    });

    it('should handle deactivation without activation', async () => {
      await expect(extension.deactivate()).resolves.not.toThrow();
    });
  });

  describe('Configuration Reload', () => {
    it('should reload configuration when settings change', async () => {
      await extension.activate();

      // Get the configuration change handler
      const configChangeHandler = require('vscode').workspace.onDidChangeConfiguration.mock.calls[0][0];

      // Simulate configuration change
      const mockEvent = {
        affectsConfiguration: jest.fn().mockReturnValue(true),
      };

      await configChangeHandler(mockEvent);

      expect(mockEvent.affectsConfiguration).toHaveBeenCalledWith('uiorbit');
      expect(mockConfigService.reload).toHaveBeenCalled();
    });

    it('should ignore non-uiorbit configuration changes', async () => {
      await extension.activate();

      const configChangeHandler = require('vscode').workspace.onDidChangeConfiguration.mock.calls[0][0];

      const mockEvent = {
        affectsConfiguration: jest.fn().mockReturnValue(false),
      };

      await configChangeHandler(mockEvent);

      expect(mockEvent.affectsConfiguration).toHaveBeenCalledWith('uiorbit');
      expect(mockConfigService.reload).not.toHaveBeenCalled();
    });

    it('should handle configuration reload errors', async () => {
      mockConfigService.reload.mockRejectedValue(new Error('Reload failed'));

      await extension.activate();

      const configChangeHandler = require('vscode').workspace.onDidChangeConfiguration.mock.calls[0][0];

      const mockEvent = {
        affectsConfiguration: jest.fn().mockReturnValue(true),
      };

      // Should not throw even if reload fails
      await expect(Promise.resolve(configChangeHandler(mockEvent))).resolves.not.toThrow();
    });
  });

  describe('Command Execution', () => {
    it('should execute openChat command', async () => {
      const mockWebviewProvider = {
        createChatPanel: jest.fn(),
      };

      // Mock the UIOrbitWebviewProvider constructor
      const UIOrbitWebviewProvider = require('../../webview/UIOrbitWebviewProvider').UIOrbitWebviewProvider;
      jest.spyOn(UIOrbitWebviewProvider.prototype, 'createChatPanel').mockImplementation(mockWebviewProvider.createChatPanel);

      await extension.activate();

      // Get the openChat command handler
      const openChatHandler = require('vscode').commands.registerCommand.mock.calls
        .find((call: any) => call[0] === 'uiorbit.openChat')[1];

      await openChatHandler();

      expect(mockWebviewProvider.createChatPanel).toHaveBeenCalled();
    });

    it('should execute generateComponent command with URI', async () => {
      const mockCommandService = {
        generateComponent: jest.fn(),
      };

      (CommandService as jest.MockedClass<typeof CommandService>).mockImplementation(() => mockCommandService as any);

      // Update service registry to return the command service when requested
      mockServiceRegistry.get.mockImplementation((serviceName: string) => {
        switch (serviceName) {
          case 'configuration':
            return mockConfigService;
          case 'command':
            return mockCommandService;
          case 'fileWatching':
            return { stopWatching: jest.fn(), dispose: jest.fn() };
          default:
            return undefined;
        }
      });

      await extension.activate();

      const generateComponentHandler = require('vscode').commands.registerCommand.mock.calls
        .find((call: any) => call[0] === 'uiorbit.generateComponent')[1];

      const mockUri = { fsPath: '/test/path' };
      generateComponentHandler(mockUri);

      expect(mockCommandService.generateComponent).toHaveBeenCalledWith(mockUri);
    });

    it('should execute analyzeCode command', async () => {
      const mockCommandService = {
        analyzeCode: jest.fn(),
      };

      (CommandService as jest.MockedClass<typeof CommandService>).mockImplementation(() => mockCommandService as any);

      // Update service registry to return the command service when requested
      mockServiceRegistry.get.mockImplementation((serviceName: string) => {
        switch (serviceName) {
          case 'configuration':
            return mockConfigService;
          case 'command':
            return mockCommandService;
          case 'fileWatching':
            return { stopWatching: jest.fn(), dispose: jest.fn() };
          default:
            return undefined;
        }
      });

      await extension.activate();

      const analyzeCodeHandler = require('vscode').commands.registerCommand.mock.calls
        .find((call: any) => call[0] === 'uiorbit.analyzeCode')[1];

      analyzeCodeHandler();

      expect(mockCommandService.analyzeCode).toHaveBeenCalled();
    });
  });
});
