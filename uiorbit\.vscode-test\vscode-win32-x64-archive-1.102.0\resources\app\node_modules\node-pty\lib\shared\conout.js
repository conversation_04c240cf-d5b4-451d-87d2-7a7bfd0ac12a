"use strict";
/**
 * Copyright (c) 2020, Microsoft Corporation (MIT License).
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWorkerPipeName = void 0;
function getWorkerPipeName(conoutPipeName) {
    return conoutPipeName + "-worker";
}
exports.getWorkerPipeName = getWorkerPipeName;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/cb0c47c0cfaad0757385834bd89d410c78a856c0/node_modules/node-pty/lib/shared/conout.js.map