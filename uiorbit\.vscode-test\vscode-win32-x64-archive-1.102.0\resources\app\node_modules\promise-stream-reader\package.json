{"name": "promise-stream-reader", "version": "1.0.1", "description": "Consume Node.js readable streams using promises.", "homepage": "https://github.com/princjef/promise-stream-reader#readme", "repository": {"type": "git", "url": "https://github.com/princjef/promise-stream-reader.git"}, "bugs": {"url": "https://github.com/princjef/promise-stream-reader/issues"}, "engines": {"node": ">8.0.0"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"commit": "commit", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "tslint --project tsconfig.json --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean && npm run lint", "build": "tsc", "watch": "tsc -w", "pretest": "npm run build", "test": "nyc ava dist/**/*.spec.js", "pretest-nocover": "npm run build", "test-nocover": "ava dist/**/*.spec.js", "show-coverage": "opener ./coverage/index.html", "coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "prerelease": "npm run build", "release": "semantic-release"}, "keywords": ["promise", "stream", "readable", "pipe"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@commitlint/cli": "^6.1.3", "@commitlint/config-conventional": "^6.1.3", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "@types/node": "^8.10.10", "ava": "^0.25.0", "codecov": "^3.0.1", "husky": "^0.14.3", "nyc": "^11.7.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "semantic-release": "^15.1.7", "tslint": "^5.9.1", "tslint-config-standard": "^7.0.0", "typescript": "^2.8.3"}, "nyc": {"sourceMap": true, "reporter": ["text", "html", "json"], "include": ["dist/**/*.js"], "exclude": ["dist/**/*.spec.*", "dist/**/*.d.ts", "dist/**/*.map*"], "check-coverage": true, "lines": 100, "statements": 100, "functions": 100, "branches": 100, "watermarks": {"lines": [100, 100], "statements": [100, 100], "functions": [100, 100], "branches": [100, 100]}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}