/**
 * Jest setup file for UIOrbit extension tests
 * This file runs before each test suite
 */

// Mock VS Code module globally - using inline mock to avoid circular dependencies
jest.mock('vscode', () => ({
  Uri: {
    file: jest.fn((path) => ({ fsPath: path, scheme: 'file', path })),
    parse: jest.fn((value) => ({ fsPath: value, scheme: 'file', path: value })),
  },
  Range: jest.fn(),
  Position: jest.fn(),
  Selection: jest.fn(),
  workspace: {
    getConfiguration: jest.fn(() => ({
      get: jest.fn(),
      update: jest.fn(),
      has: jest.fn(),
      inspect: jest.fn(),
    })),
    workspaceFolders: [],
    onDidChangeConfiguration: jest.fn(),
    onDidChangeWorkspaceFolders: jest.fn(),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      createDirectory: jest.fn(),
      delete: jest.fn(),
      stat: jest.fn(),
      readDirectory: jest.fn(),
    },
  },
  window: {
    showInformationMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showInputBox: jest.fn(),
    showQuickPick: jest.fn(),
    createWebviewPanel: jest.fn(),
    createOutputChannel: jest.fn(() => ({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn(),
    })),
    withProgress: jest.fn((options, task) => {
      const progress = { report: jest.fn() };
      return task(progress);
    }),
    activeTextEditor: undefined,
    onDidChangeActiveTextEditor: jest.fn(),
  },
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn(),
  },
  languages: {
    registerCompletionItemProvider: jest.fn(),
    registerHoverProvider: jest.fn(),
  },
  ExtensionContext: jest.fn(),
  Disposable: jest.fn(() => ({ dispose: jest.fn() })),
  EventEmitter: jest.fn(),
  TreeDataProvider: jest.fn(),
  WebviewPanel: jest.fn(),
  ViewColumn: {
    One: 1,
    Two: 2,
    Three: 3,
  },
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2,
    WorkspaceFolder: 3,
  },
}), { virtual: true });

// Mock console methods to reduce noise in tests
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    // Keep error and warn for debugging
    error: originalConsole.error,
    warn: originalConsole.warn,
    // Mock info and log to reduce noise
    info: jest.fn(),
    log: jest.fn(),
    debug: jest.fn(),
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// Global test utilities
global.testUtils = {
  // Helper to create mock VS Code context
  createMockContext: () => ({
    subscriptions: [],
    workspaceState: {
      get: jest.fn(),
      update: jest.fn(),
    },
    globalState: {
      get: jest.fn(),
      update: jest.fn(),
    },
    extensionUri: {
      fsPath: '/mock/extension/path',
      scheme: 'file',
    },
    extensionPath: '/mock/extension/path',
    asAbsolutePath: jest.fn((path: string) => `/mock/extension/path/${path}`),
    storageUri: {
      fsPath: '/mock/storage/path',
      scheme: 'file',
    },
    globalStorageUri: {
      fsPath: '/mock/global/storage/path',
      scheme: 'file',
    },
    logUri: {
      fsPath: '/mock/log/path',
      scheme: 'file',
    },
    extensionMode: 1, // Normal mode
  }),

  // Helper to create mock file system
  createMockFileSystem: () => ({
    readFile: jest.fn(),
    writeFile: jest.fn(),
    createDirectory: jest.fn(),
    delete: jest.fn(),
    stat: jest.fn(),
    readDirectory: jest.fn(),
  }),

  // Helper to create mock workspace
  createMockWorkspace: () => ({
    workspaceFolders: [
      {
        uri: {
          fsPath: '/mock/workspace',
          scheme: 'file',
        },
        name: 'test-workspace',
        index: 0,
      },
    ],
    getConfiguration: jest.fn(() => ({
      get: jest.fn(),
      update: jest.fn(),
      has: jest.fn(),
      inspect: jest.fn(),
    })),
  }),

  // Helper to wait for async operations
  waitFor: (ms: number = 0) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock disposable
  createMockDisposable: () => ({
    dispose: jest.fn(),
  }),
};

// Extend Jest matchers if needed
declare global {
  namespace jest {
    interface Matchers<R> {
      // Add custom matchers here if needed
    }
  }
  
  var testUtils: {
    createMockContext: () => any;
    createMockFileSystem: () => any;
    createMockWorkspace: () => any;
    waitFor: (ms?: number) => Promise<void>;
    createMockDisposable: () => any;
  };
}

export {};
