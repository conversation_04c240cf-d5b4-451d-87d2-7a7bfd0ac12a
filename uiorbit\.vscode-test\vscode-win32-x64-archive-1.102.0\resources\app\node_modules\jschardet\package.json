{"name": "jschardet", "version": "3.1.4", "description": "Character encoding auto-detection in JavaScript (port of python's chardet)", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "LGPL-2.1+", "keywords": ["encoding", "charset"], "repository": {"type": "git", "url": "https://github.com/aadsm/jschardet.git"}, "types": "index.d.ts", "directories": {"lib": "./lib", "test": "./test"}, "devDependencies": {"browserify": "~17.0.0", "google-closure-compiler": "20240317.0.0", "jest": "^29.7.0"}, "scripts": {"dist-dev": "mkdir -p dist && browserify index.js -s jschardet --detect-globals false -o dist/jschardet.js && ./scripts/show-size-changes.sh dist/jschardet.js", "dist": "npm run dist-dev && java -jar node_modules/google-closure-compiler-java/compiler.jar --warning_level QUIET --compilation_level SIMPLE_OPTIMIZATIONS --language_in=ECMASCRIPT6_STRICT --language_out=ES5 --js dist/jschardet.js > dist/jschardet.min.js && ./scripts/show-size-changes.sh dist/jschardet.min.js", "dist-size-changes": "./scripts/show-size-changes.sh dist/*", "test": "jest"}, "engines": {"node": ">=0.1.90"}}