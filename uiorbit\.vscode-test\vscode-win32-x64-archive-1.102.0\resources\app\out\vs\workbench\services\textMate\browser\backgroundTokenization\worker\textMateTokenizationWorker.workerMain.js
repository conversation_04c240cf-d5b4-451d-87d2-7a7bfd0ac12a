/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var $t=function(e,t){return $t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},$t(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");$t(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}export var __assign=function(){return __assign=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}export function __decorate(e,t,r,n){var i=arguments.length,o=i<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,r):n,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(e,t,r,n);else for(var l=e.length-1;l>=0;l--)(a=e[l])&&(o=(i<3?a(o):i>3?a(t,r,o):a(t,r))||o);return i>3&&o&&Object.defineProperty(t,r,o),o}export function __param(e,t){return function(r,n){t(r,n,e)}}export function __esDecorate(e,t,r,n,i,o){function a(q){if(q!==void 0&&typeof q!="function")throw new TypeError("Function expected");return q}for(var l=n.kind,u=l==="getter"?"get":l==="setter"?"set":"value",c=!t&&e?n.static?e:e.prototype:null,f=t||(c?Object.getOwnPropertyDescriptor(c,n.name):{}),h,b=!1,w=r.length-1;w>=0;w--){var k={};for(var T in n)k[T]=T==="access"?{}:n[T];for(var T in n.access)k.access[T]=n.access[T];k.addInitializer=function(q){if(b)throw new TypeError("Cannot add initializers after decoration has completed");o.push(a(q||null))};var D=(0,r[w])(l==="accessor"?{get:f.get,set:f.set}:f[u],k);if(l==="accessor"){if(D===void 0)continue;if(D===null||typeof D!="object")throw new TypeError("Object expected");(h=a(D.get))&&(f.get=h),(h=a(D.set))&&(f.set=h),(h=a(D.init))&&i.unshift(h)}else(h=a(D))&&(l==="field"?i.unshift(h):f[u]=h)}c&&Object.defineProperty(c,n.name,f),b=!0}export function __runInitializers(e,t,r){for(var n=arguments.length>2,i=0;i<t.length;i++)r=n?t[i].call(e,r):t[i].call(e);return n?r:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,r){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,r,n){function i(o){return o instanceof r?o:new r(function(a){a(o)})}return new(r||(r=Promise))(function(o,a){function l(f){try{c(n.next(f))}catch(h){a(h)}}function u(f){try{c(n.throw(f))}catch(h){a(h)}}function c(f){f.done?o(f.value):i(f.value).then(l,u)}c((n=n.apply(e,t||[])).next())})}export function __generator(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,i,o,a;return a={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function l(c){return function(f){return u([c,f])}}function u(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(r=0)),r;)try{if(n=1,i&&(o=c[0]&2?i.return:c[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,c[1])).done)return o;switch(i=0,o&&(c=[c[0]&2,o.value]),c[0]){case 0:case 1:o=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,i=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!o||c[1]>o[0]&&c[1]<o[3])){r.label=c[1];break}if(c[0]===6&&r.label<o[1]){r.label=o[1],o=c;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(c);break}o[2]&&r.ops.pop(),r.trys.pop();continue}c=t.call(e,r)}catch(f){c=[6,f],i=0}finally{n=o=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,r,n){n===void 0&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]};export function __exportStar(e,t){for(var r in e)r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r)&&__createBinding(t,e,r)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),i,o=[],a;try{for(;(t===void 0||t-- >0)&&!(i=n.next()).done;)o.push(i.value)}catch(l){a={error:l}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return o}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),i=0,t=0;t<r;t++)for(var o=arguments[t],a=0,l=o.length;a<l;a++,i++)n[i]=o[a];return n}export function __spreadArray(e,t,r){if(r||arguments.length===2)for(var n=0,i=t.length,o;n<i;n++)(o||!(n in t))&&(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),i,o=[];return i={},l("next"),l("throw"),l("return",a),i[Symbol.asyncIterator]=function(){return this},i;function a(w){return function(k){return Promise.resolve(k).then(w,h)}}function l(w,k){n[w]&&(i[w]=function(T){return new Promise(function(D,q){o.push([w,T,D,q])>1||u(w,T)})},k&&(i[w]=k(i[w])))}function u(w,k){try{c(n[w](k))}catch(T){b(o[0][3],T)}}function c(w){w.value instanceof __await?Promise.resolve(w.value.v).then(f,h):b(o[0][2],w)}function f(w){u("next",w)}function h(w){u("throw",w)}function b(w,k){w(k),o.shift(),o.length&&u(o[0][0],o[0][1])}}export function __asyncDelegator(e){var t,r;return t={},n("next"),n("throw",function(i){throw i}),n("return"),t[Symbol.iterator]=function(){return this},t;function n(i,o){t[i]=e[i]?function(a){return(r=!r)?{value:__await(e[i](a)),done:!1}:o?o(a):a}:o}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(o){r[o]=e[o]&&function(a){return new Promise(function(l,u){a=e[o](a),i(l,u,a.done,a.value)})}}function i(o,a,l,u){Promise.resolve(u).then(function(c){o({value:c,done:l})},a)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var Tn=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)r!=="default"&&Object.prototype.hasOwnProperty.call(e,r)&&__createBinding(t,e,r);return Tn(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,r,n){if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?n:r==="a"?n.call(e):n?n.value:t.get(e)}export function __classPrivateFieldSet(e,t,r,n,i){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?i.call(e,r):i?i.value=r:t.set(e,r),r}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,r){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var n,i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(n===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(i=n)}if(typeof n!="function")throw new TypeError("Object not disposable.");i&&(n=function(){try{i.call(this)}catch(o){return Promise.reject(o)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var Dn=typeof SuppressedError=="function"?SuppressedError:function(e,t,r){var n=new Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};export function __disposeResources(e){function t(n){e.error=e.hasError?new Dn(n,e.error,"An error was suppressed during disposal."):n,e.hasError=!0}function r(){for(;e.stack.length;){var n=e.stack.pop();try{var i=n.dispose&&n.dispose.call(n.value);if(n.async)return Promise.resolve(i).then(r,function(o){return t(o),r()})}catch(o){t(o)}}if(e.hasError)throw e.error}return r()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};function Pn(){return globalThis._VSCODE_NLS_MESSAGES}function d1(){return globalThis._VSCODE_NLS_LANGUAGE}var Rn=d1()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function g1(e,t){let r;return t.length===0?r=e:r=e.replace(/\{(\d+)\}/g,(n,i)=>{const o=i[0],a=t[o];let l=n;return typeof a=="string"?l=a:(typeof a=="number"||typeof a=="boolean"||a===void 0||a===null)&&(l=String(a)),l}),Rn&&(r="\uFF3B"+r.replace(/[aouei]/g,"$&$&")+"\uFF3D"),r}function y(e,t,...r){return g1(typeof e=="number"?In(e,t):t,r)}function In(e,t){const r=Pn()?.[e];if(typeof r!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return r}var _e="en",Ke=!1,et=!1,ze=!1,Mn=!1,b1=!1,xt=!1,Fn=!1,jn=!1,Un=!1,zn=!1,tt=void 0,rt=_e,m1=_e,qn=void 0,se=void 0,oe=globalThis,Q=void 0;typeof oe.vscode<"u"&&typeof oe.vscode.process<"u"?Q=oe.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(Q=process);var p1=typeof Q?.versions?.electron=="string",Bn=p1&&Q?.type==="renderer";if(typeof Q=="object"){Ke=Q.platform==="win32",et=Q.platform==="darwin",ze=Q.platform==="linux",Mn=ze&&!!Q.env.SNAP&&!!Q.env.SNAP_REVISION,Fn=p1,Un=!!Q.env.CI||!!Q.env.BUILD_ARTIFACTSTAGINGDIRECTORY,tt=_e,rt=_e;const e=Q.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);tt=t.userLocale,m1=t.osLocale,rt=t.resolvedLanguage||_e,qn=t.languagePack?.translationsConfigFile}catch{}b1=!0}else typeof navigator=="object"&&!Bn?(se=navigator.userAgent,Ke=se.indexOf("Windows")>=0,et=se.indexOf("Macintosh")>=0,jn=(se.indexOf("Macintosh")>=0||se.indexOf("iPad")>=0||se.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,ze=se.indexOf("Linux")>=0,zn=se?.indexOf("Mobi")>=0,xt=!0,rt=d1()||_e,tt=navigator.language.toLowerCase(),m1=tt):console.error("Unable to resolve platform.");var v1;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(v1||(v1={}));var Ot=0;et?Ot=1:Ke?Ot=3:ze&&(Ot=2);var Te=Ke,Vn=et,Wn=ze,Gn=b1,w1=xt,Hn=xt&&typeof oe.importScripts=="function",Zn=Hn?oe.origin:void 0,ne=se,be=rt,y1;(function(e){function t(){return be}e.value=t;function r(){return be.length===2?be==="en":be.length>=3?be[0]==="e"&&be[1]==="n"&&be[2]==="-":!1}e.isDefaultVariant=r;function n(){return be==="en"}e.isDefault=n})(y1||(y1={}));var Yn=typeof oe.postMessage=="function"&&!oe.importScripts,E1=(()=>{if(Yn){const e=[];oe.addEventListener("message",r=>{if(r.data&&r.data.vscodeScheduleAsyncWork)for(let n=0,i=e.length;n<i;n++){const o=e[n];if(o.id===r.data.vscodeScheduleAsyncWork){e.splice(n,1),o.callback();return}}});let t=0;return r=>{const n=++t;e.push({id:n,callback:r}),oe.postMessage({vscodeScheduleAsyncWork:n},"*")}}return e=>setTimeout(e)})(),N1;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(N1||(N1={}));var Jn=!!(ne&&ne.indexOf("Chrome")>=0),Po=!!(ne&&ne.indexOf("Firefox")>=0),Ro=!!(!Jn&&ne&&ne.indexOf("Safari")>=0),Io=!!(ne&&ne.indexOf("Edg/")>=0),Mo=!!(ne&&ne.indexOf("Android")>=0),we,_t=globalThis.vscode;if(typeof _t<"u"&&typeof _t.process<"u"){const e=_t.process;we={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?we={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:we={get platform(){return Te?"win32":Vn?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var nt=we.cwd,Tt=we.env,Qn=we.platform,Fo=we.arch,Xn=65,Kn=97,ei=90,ti=122,ye=46,z=47,Z=92,ae=58,ri=63,L1=class extends Error{constructor(e,t,r){let n;typeof t=="string"&&t.indexOf("not ")===0?(n="must not be",t=t.replace(/^not /,"")):n="must be";const i=e.indexOf(".")!==-1?"property":"argument";let o=`The "${e}" ${i} ${n} of type ${t}`;o+=`. Received type ${typeof r}`,super(o),this.code="ERR_INVALID_ARG_TYPE"}};function ni(e,t){if(e===null||typeof e!="object")throw new L1(t,"Object",e)}function P(e,t){if(typeof e!="string")throw new L1(t,"string",e)}var Y=Qn==="win32";function C(e){return e===z||e===Z}function Dt(e){return e===z}function le(e){return e>=Xn&&e<=ei||e>=Kn&&e<=ti}function it(e,t,r,n){let i="",o=0,a=-1,l=0,u=0;for(let c=0;c<=e.length;++c){if(c<e.length)u=e.charCodeAt(c);else{if(n(u))break;u=z}if(n(u)){if(!(a===c-1||l===1))if(l===2){if(i.length<2||o!==2||i.charCodeAt(i.length-1)!==ye||i.charCodeAt(i.length-2)!==ye){if(i.length>2){const f=i.lastIndexOf(r);f===-1?(i="",o=0):(i=i.slice(0,f),o=i.length-1-i.lastIndexOf(r)),a=c,l=0;continue}else if(i.length!==0){i="",o=0,a=c,l=0;continue}}t&&(i+=i.length>0?`${r}..`:"..",o=2)}else i.length>0?i+=`${r}${e.slice(a+1,c)}`:i=e.slice(a+1,c),o=c-a-1;a=c,l=0}else u===ye&&l!==-1?++l:l=-1}return i}function ii(e){return e?`${e[0]==="."?"":"."}${e}`:""}function C1(e,t){ni(t,"pathObject");const r=t.dir||t.root,n=t.base||`${t.name||""}${ii(t.ext)}`;return r?r===t.root?`${r}${n}`:`${r}${e}${n}`:n}var R={resolve(...e){let t="",r="",n=!1;for(let i=e.length-1;i>=-1;i--){let o;if(i>=0){if(o=e[i],P(o,`paths[${i}]`),o.length===0)continue}else t.length===0?o=nt():(o=Tt[`=${t}`]||nt(),(o===void 0||o.slice(0,2).toLowerCase()!==t.toLowerCase()&&o.charCodeAt(2)===Z)&&(o=`${t}\\`));const a=o.length;let l=0,u="",c=!1;const f=o.charCodeAt(0);if(a===1)C(f)&&(l=1,c=!0);else if(C(f))if(c=!0,C(o.charCodeAt(1))){let h=2,b=h;for(;h<a&&!C(o.charCodeAt(h));)h++;if(h<a&&h!==b){const w=o.slice(b,h);for(b=h;h<a&&C(o.charCodeAt(h));)h++;if(h<a&&h!==b){for(b=h;h<a&&!C(o.charCodeAt(h));)h++;(h===a||h!==b)&&(u=`\\\\${w}\\${o.slice(b,h)}`,l=h)}}}else l=1;else le(f)&&o.charCodeAt(1)===ae&&(u=o.slice(0,2),l=2,a>2&&C(o.charCodeAt(2))&&(c=!0,l=3));if(u.length>0)if(t.length>0){if(u.toLowerCase()!==t.toLowerCase())continue}else t=u;if(n){if(t.length>0)break}else if(r=`${o.slice(l)}\\${r}`,n=c,c&&t.length>0)break}return r=it(r,!n,"\\",C),n?`${t}\\${r}`:`${t}${r}`||"."},normalize(e){P(e,"path");const t=e.length;if(t===0)return".";let r=0,n,i=!1;const o=e.charCodeAt(0);if(t===1)return Dt(o)?"\\":e;if(C(o))if(i=!0,C(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!C(e.charCodeAt(l));)l++;if(l<t&&l!==u){const c=e.slice(u,l);for(u=l;l<t&&C(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!C(e.charCodeAt(l));)l++;if(l===t)return`\\\\${c}\\${e.slice(u)}\\`;l!==u&&(n=`\\\\${c}\\${e.slice(u,l)}`,r=l)}}}else r=1;else le(o)&&e.charCodeAt(1)===ae&&(n=e.slice(0,2),r=2,t>2&&C(e.charCodeAt(2))&&(i=!0,r=3));let a=r<t?it(e.slice(r),!i,"\\",C):"";if(a.length===0&&!i&&(a="."),a.length>0&&C(e.charCodeAt(t-1))&&(a+="\\"),!i&&n===void 0&&e.includes(":")){if(a.length>=2&&le(a.charCodeAt(0))&&a.charCodeAt(1)===ae)return`.\\${a}`;let l=e.indexOf(":");do if(l===t-1||C(e.charCodeAt(l+1)))return`.\\${a}`;while((l=e.indexOf(":",l+1))!==-1)}return n===void 0?i?`\\${a}`:a:i?`${n}\\${a}`:`${n}${a}`},isAbsolute(e){P(e,"path");const t=e.length;if(t===0)return!1;const r=e.charCodeAt(0);return C(r)||t>2&&le(r)&&e.charCodeAt(1)===ae&&C(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,r;for(let o=0;o<e.length;++o){const a=e[o];P(a,"path"),a.length>0&&(t===void 0?t=r=a:t+=`\\${a}`)}if(t===void 0)return".";let n=!0,i=0;if(typeof r=="string"&&C(r.charCodeAt(0))){++i;const o=r.length;o>1&&C(r.charCodeAt(1))&&(++i,o>2&&(C(r.charCodeAt(2))?++i:n=!1))}if(n){for(;i<t.length&&C(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return R.normalize(t)},relative(e,t){if(P(e,"from"),P(t,"to"),e===t)return"";const r=R.resolve(e),n=R.resolve(t);if(r===n||(e=r.toLowerCase(),t=n.toLowerCase(),e===t))return"";if(r.length!==e.length||n.length!==t.length){const k=r.split("\\"),T=n.split("\\");k[k.length-1]===""&&k.pop(),T[T.length-1]===""&&T.pop();const D=k.length,q=T.length,ge=D<q?D:q;let j;for(j=0;j<ge&&k[j].toLowerCase()===T[j].toLowerCase();j++);return j===0?n:j===ge?q>ge?T.slice(j).join("\\"):D>ge?"..\\".repeat(D-1-j)+"..":"":"..\\".repeat(D-j)+T.slice(j).join("\\")}let i=0;for(;i<e.length&&e.charCodeAt(i)===Z;)i++;let o=e.length;for(;o-1>i&&e.charCodeAt(o-1)===Z;)o--;const a=o-i;let l=0;for(;l<t.length&&t.charCodeAt(l)===Z;)l++;let u=t.length;for(;u-1>l&&t.charCodeAt(u-1)===Z;)u--;const c=u-l,f=a<c?a:c;let h=-1,b=0;for(;b<f;b++){const k=e.charCodeAt(i+b);if(k!==t.charCodeAt(l+b))break;k===Z&&(h=b)}if(b!==f){if(h===-1)return n}else{if(c>f){if(t.charCodeAt(l+b)===Z)return n.slice(l+b+1);if(b===2)return n.slice(l+b)}a>f&&(e.charCodeAt(i+b)===Z?h=b:b===2&&(h=3)),h===-1&&(h=0)}let w="";for(b=i+h+1;b<=o;++b)(b===o||e.charCodeAt(b)===Z)&&(w+=w.length===0?"..":"\\..");return l+=h,w.length>0?`${w}${n.slice(l,u)}`:(n.charCodeAt(l)===Z&&++l,n.slice(l,u))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=R.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===Z){if(t.charCodeAt(1)===Z){const r=t.charCodeAt(2);if(r!==ri&&r!==ye)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(le(t.charCodeAt(0))&&t.charCodeAt(1)===ae&&t.charCodeAt(2)===Z)return`\\\\?\\${t}`;return t},dirname(e){P(e,"path");const t=e.length;if(t===0)return".";let r=-1,n=0;const i=e.charCodeAt(0);if(t===1)return C(i)?e:".";if(C(i)){if(r=n=1,C(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!C(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&C(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!C(e.charCodeAt(l));)l++;if(l===t)return e;l!==u&&(r=n=l+1)}}}}else le(i)&&e.charCodeAt(1)===ae&&(r=t>2&&C(e.charCodeAt(2))?3:2,n=r);let o=-1,a=!0;for(let l=t-1;l>=n;--l)if(C(e.charCodeAt(l))){if(!a){o=l;break}}else a=!1;if(o===-1){if(r===-1)return".";o=r}return e.slice(0,o)},basename(e,t){t!==void 0&&P(t,"suffix"),P(e,"path");let r=0,n=-1,i=!0,o;if(e.length>=2&&le(e.charCodeAt(0))&&e.charCodeAt(1)===ae&&(r=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(o=e.length-1;o>=r;--o){const u=e.charCodeAt(o);if(C(u)){if(!i){r=o+1;break}}else l===-1&&(i=!1,l=o+1),a>=0&&(u===t.charCodeAt(a)?--a===-1&&(n=o):(a=-1,n=l))}return r===n?n=l:n===-1&&(n=e.length),e.slice(r,n)}for(o=e.length-1;o>=r;--o)if(C(e.charCodeAt(o))){if(!i){r=o+1;break}}else n===-1&&(i=!1,n=o+1);return n===-1?"":e.slice(r,n)},extname(e){P(e,"path");let t=0,r=-1,n=0,i=-1,o=!0,a=0;e.length>=2&&e.charCodeAt(1)===ae&&le(e.charCodeAt(0))&&(t=n=2);for(let l=e.length-1;l>=t;--l){const u=e.charCodeAt(l);if(C(u)){if(!o){n=l+1;break}continue}i===-1&&(o=!1,i=l+1),u===ye?r===-1?r=l:a!==1&&(a=1):r!==-1&&(a=-1)}return r===-1||i===-1||a===0||a===1&&r===i-1&&r===n+1?"":e.slice(r,i)},format:C1.bind(null,"\\"),parse(e){P(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.length;let n=0,i=e.charCodeAt(0);if(r===1)return C(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(C(i)){if(n=1,C(e.charCodeAt(1))){let h=2,b=h;for(;h<r&&!C(e.charCodeAt(h));)h++;if(h<r&&h!==b){for(b=h;h<r&&C(e.charCodeAt(h));)h++;if(h<r&&h!==b){for(b=h;h<r&&!C(e.charCodeAt(h));)h++;h===r?n=h:h!==b&&(n=h+1)}}}}else if(le(i)&&e.charCodeAt(1)===ae){if(r<=2)return t.root=t.dir=e,t;if(n=2,C(e.charCodeAt(2))){if(r===3)return t.root=t.dir=e,t;n=3}}n>0&&(t.root=e.slice(0,n));let o=-1,a=n,l=-1,u=!0,c=e.length-1,f=0;for(;c>=n;--c){if(i=e.charCodeAt(c),C(i)){if(!u){a=c+1;break}continue}l===-1&&(u=!1,l=c+1),i===ye?o===-1?o=c:f!==1&&(f=1):o!==-1&&(f=-1)}return l!==-1&&(o===-1||f===0||f===1&&o===l-1&&o===a+1?t.base=t.name=e.slice(a,l):(t.name=e.slice(a,o),t.base=e.slice(a,l),t.ext=e.slice(o,l))),a>0&&a!==n?t.dir=e.slice(0,a-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},si=(()=>{if(Y){const e=/\\/g;return()=>{const t=nt().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>nt()})(),O={resolve(...e){let t="",r=!1;for(let n=e.length-1;n>=0&&!r;n--){const i=e[n];P(i,`paths[${n}]`),i.length!==0&&(t=`${i}/${t}`,r=i.charCodeAt(0)===z)}if(!r){const n=si();t=`${n}/${t}`,r=n.charCodeAt(0)===z}return t=it(t,!r,"/",Dt),r?`/${t}`:t.length>0?t:"."},normalize(e){if(P(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===z,r=e.charCodeAt(e.length-1)===z;return e=it(e,!t,"/",Dt),e.length===0?t?"/":r?"./":".":(r&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return P(e,"path"),e.length>0&&e.charCodeAt(0)===z},join(...e){if(e.length===0)return".";const t=[];for(let r=0;r<e.length;++r){const n=e[r];P(n,"path"),n.length>0&&t.push(n)}return t.length===0?".":O.normalize(t.join("/"))},relative(e,t){if(P(e,"from"),P(t,"to"),e===t||(e=O.resolve(e),t=O.resolve(t),e===t))return"";const r=1,n=e.length,i=n-r,o=1,a=t.length-o,l=i<a?i:a;let u=-1,c=0;for(;c<l;c++){const h=e.charCodeAt(r+c);if(h!==t.charCodeAt(o+c))break;h===z&&(u=c)}if(c===l)if(a>l){if(t.charCodeAt(o+c)===z)return t.slice(o+c+1);if(c===0)return t.slice(o+c)}else i>l&&(e.charCodeAt(r+c)===z?u=c:c===0&&(u=0));let f="";for(c=r+u+1;c<=n;++c)(c===n||e.charCodeAt(c)===z)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(o+u)}`},toNamespacedPath(e){return e},dirname(e){if(P(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===z;let r=-1,n=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===z){if(!n){r=i;break}}else n=!1;return r===-1?t?"/":".":t&&r===1?"//":e.slice(0,r)},basename(e,t){t!==void 0&&P(t,"suffix"),P(e,"path");let r=0,n=-1,i=!0,o;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(o=e.length-1;o>=0;--o){const u=e.charCodeAt(o);if(u===z){if(!i){r=o+1;break}}else l===-1&&(i=!1,l=o+1),a>=0&&(u===t.charCodeAt(a)?--a===-1&&(n=o):(a=-1,n=l))}return r===n?n=l:n===-1&&(n=e.length),e.slice(r,n)}for(o=e.length-1;o>=0;--o)if(e.charCodeAt(o)===z){if(!i){r=o+1;break}}else n===-1&&(i=!1,n=o+1);return n===-1?"":e.slice(r,n)},extname(e){P(e,"path");let t=-1,r=0,n=-1,i=!0,o=0;for(let a=e.length-1;a>=0;--a){const l=e[a];if(l==="/"){if(!i){r=a+1;break}continue}n===-1&&(i=!1,n=a+1),l==="."?t===-1?t=a:o!==1&&(o=1):t!==-1&&(o=-1)}return t===-1||n===-1||o===0||o===1&&t===n-1&&t===r+1?"":e.slice(t,n)},format:C1.bind(null,"/"),parse(e){P(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.charCodeAt(0)===z;let n;r?(t.root="/",n=1):n=0;let i=-1,o=0,a=-1,l=!0,u=e.length-1,c=0;for(;u>=n;--u){const f=e.charCodeAt(u);if(f===z){if(!l){o=u+1;break}continue}a===-1&&(l=!1,a=u+1),f===ye?i===-1?i=u:c!==1&&(c=1):i!==-1&&(c=-1)}if(a!==-1){const f=o===0&&r?1:o;i===-1||c===0||c===1&&i===a-1&&i===o+1?t.base=t.name=e.slice(f,a):(t.name=e.slice(f,i),t.base=e.slice(f,a),t.ext=e.slice(i,a))}return o>0?t.dir=e.slice(0,o-1):r&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};O.win32=R.win32=R,O.posix=R.posix=O;var oi=Y?R.normalize:O.normalize,jo=Y?R.isAbsolute:O.isAbsolute,ai=Y?R.join:O.join,li=Y?R.resolve:O.resolve,ui=Y?R.relative:O.relative,ci=Y?R.dirname:O.dirname,Uo=Y?R.basename:O.basename,zo=Y?R.extname:O.extname,qo=Y?R.format:O.format,Bo=Y?R.parse:O.parse,Vo=Y?R.toNamespacedPath:O.toNamespacedPath,st=Y?R.sep:O.sep,Wo=Y?R.delimiter:O.delimiter,hi=/^\w[\w\d+.-]*$/,fi=/^\//,di=/^\/\//;function gi(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!hi.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!fi.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(di.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function bi(e,t){return!e&&!t?"file":e}function mi(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==re&&(t=re+t):t=re;break}return t}var _="",re="/",pi=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,J=class yt{static isUri(t){return t instanceof yt?!0:!t||typeof t!="object"?!1:typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function"}constructor(t,r,n,i,o,a=!1){typeof t=="object"?(this.scheme=t.scheme||_,this.authority=t.authority||_,this.path=t.path||_,this.query=t.query||_,this.fragment=t.fragment||_):(this.scheme=bi(t,a),this.authority=r||_,this.path=mi(this.scheme,n||_),this.query=i||_,this.fragment=o||_,gi(this,a))}get fsPath(){return ot(this,!1)}with(t){if(!t)return this;let{scheme:r,authority:n,path:i,query:o,fragment:a}=t;return r===void 0?r=this.scheme:r===null&&(r=_),n===void 0?n=this.authority:n===null&&(n=_),i===void 0?i=this.path:i===null&&(i=_),o===void 0?o=this.query:o===null&&(o=_),a===void 0?a=this.fragment:a===null&&(a=_),r===this.scheme&&n===this.authority&&i===this.path&&o===this.query&&a===this.fragment?this:new De(r,n,i,o,a)}static parse(t,r=!1){const n=pi.exec(t);return n?new De(n[2]||_,at(n[4]||_),at(n[5]||_),at(n[7]||_),at(n[9]||_),r):new De(_,_,_,_,_)}static file(t){let r=_;if(Te&&(t=t.replace(/\\/g,re)),t[0]===re&&t[1]===re){const n=t.indexOf(re,2);n===-1?(r=t.substring(2),t=re):(r=t.substring(2,n),t=t.substring(n)||re)}return new De("file",r,t,_,_)}static from(t,r){return new De(t.scheme,t.authority,t.path,t.query,t.fragment,r)}static joinPath(t,...r){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return Te&&t.scheme==="file"?n=yt.file(R.join(ot(t,!0),...r)).path:n=O.join(t.path,...r),t.with({path:n})}toString(t=!1){return Pt(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof yt)return t;{const r=new De(t);return r._formatted=t.external??null,r._fsPath=t._sep===A1?t.fsPath??null:null,r}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},A1=Te?1:void 0,De=class extends J{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=ot(this,!1)),this._fsPath}toString(e=!1){return e?Pt(this,!0):(this._formatted||(this._formatted=Pt(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=A1),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},k1={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function S1(e,t,r){let n,i=-1;for(let o=0;o<e.length;o++){const a=e.charCodeAt(o);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===45||a===46||a===95||a===126||t&&a===47||r&&a===91||r&&a===93||r&&a===58)i!==-1&&(n+=encodeURIComponent(e.substring(i,o)),i=-1),n!==void 0&&(n+=e.charAt(o));else{n===void 0&&(n=e.substr(0,o));const l=k1[a];l!==void 0?(i!==-1&&(n+=encodeURIComponent(e.substring(i,o)),i=-1),n+=l):i===-1&&(i=o)}}return i!==-1&&(n+=encodeURIComponent(e.substring(i))),n!==void 0?n:e}function vi(e){let t;for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);n===35||n===63?(t===void 0&&(t=e.substr(0,r)),t+=k1[n]):t!==void 0&&(t+=e[r])}return t!==void 0?t:e}function ot(e,t){let r;return e.authority&&e.path.length>1&&e.scheme==="file"?r=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?r=e.path.substr(1):r=e.path[1].toLowerCase()+e.path.substr(2):r=e.path,Te&&(r=r.replace(/\//g,"\\")),r}function Pt(e,t){const r=t?vi:S1;let n="",{scheme:i,authority:o,path:a,query:l,fragment:u}=e;if(i&&(n+=i,n+=":"),(o||i==="file")&&(n+=re,n+=re),o){let c=o.indexOf("@");if(c!==-1){const f=o.substr(0,c);o=o.substr(c+1),c=f.lastIndexOf(":"),c===-1?n+=r(f,!1,!1):(n+=r(f.substr(0,c),!1,!1),n+=":",n+=r(f.substr(c+1),!1,!0)),n+="@"}o=o.toLowerCase(),c=o.lastIndexOf(":"),c===-1?n+=r(o,!1,!0):(n+=r(o.substr(0,c),!1,!0),n+=o.substr(c))}if(a){if(a.length>=3&&a.charCodeAt(0)===47&&a.charCodeAt(2)===58){const c=a.charCodeAt(1);c>=65&&c<=90&&(a=`/${String.fromCharCode(c+32)}:${a.substr(3)}`)}else if(a.length>=2&&a.charCodeAt(1)===58){const c=a.charCodeAt(0);c>=65&&c<=90&&(a=`${String.fromCharCode(c+32)}:${a.substr(2)}`)}n+=r(a,!0,!1)}return l&&(n+="?",n+=r(l,!1,!1)),u&&(n+="#",n+=t?u:S1(u,!1,!1)),n}function $1(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+$1(e.substr(3)):e}}var x1=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function at(e){return e.match(x1)?e.replace(x1,t=>$1(t)):e}function O1(e,t){const r=lt(e,t);return r===-1?void 0:e[r]}function lt(e,t,r=0,n=e.length){let i=r,o=n;for(;i<o;){const a=Math.floor((i+o)/2);t(e[a])?i=a+1:o=a}return i-1}function _1(e,t,r=0,n=e.length){let i=r,o=n;for(;i<o;){const a=Math.floor((i+o)/2);t(e[a])?o=a:i=a+1}return i}var Go=class wn{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(wn.assertInvariants){if(this.d){for(const n of this.e)if(this.d(n)&&!t(n))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const r=lt(this.e,t,this.c);return this.c=r+1,r===-1?void 0:this.e[r]}},wi=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?Mt.isErrorNoTelemetry(e)?new Mt(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},T1=new wi;function ut(e){T1.onUnexpectedError(e)}function ue(e){yi(e)||T1.onUnexpectedError(e)}function Rt(e){if(e instanceof Error){const{name:t,message:r,cause:n}=e,i=e.stacktrace||e.stack;return{$isError:!0,name:t,message:r,stack:i,noTelemetry:Mt.isErrorNoTelemetry(e),cause:n?Rt(n):void 0,code:e.code}}return e}var It="Canceled";function yi(e){return e instanceof Ei?!0:e instanceof Error&&e.name===It&&e.message===It}var Ei=class extends Error{constructor(){super(It),this.name=this.message}},Ho=class Et extends Error{static{this.a="PendingMigrationError"}static is(t){return t instanceof Et||t instanceof Error&&t.name===Et.a}constructor(t){super(t),this.name=Et.a}},Mt=class a1 extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof a1)return t;const r=new a1;return r.message=t.message,r.stack=t.stack,r}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},B=class yn extends Error{constructor(t){super(t||"An unexpected bug occurred."),Object.setPrototypeOf(this,yn.prototype)}};function D1(e,t,r){const n=e.slice(0,t),i=e.slice(t);return n.concat(r,i)}var Ft;(function(e){function t(o){return o<0}e.isLessThan=t;function r(o){return o<=0}e.isLessThanOrEqual=r;function n(o){return o>0}e.isGreaterThan=n;function i(o){return o===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(Ft||(Ft={}));function P1(e,t){return(r,n)=>t(e(r),e(n))}var R1=(e,t)=>e-t,Zo=class Nt{static{this.empty=new Nt(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(r=>(t(r),!0))}toArray(){const t=[];return this.iterate(r=>(t.push(r),!0)),t}filter(t){return new Nt(r=>this.iterate(n=>t(n)?r(n):!0))}map(t){return new Nt(r=>this.iterate(n=>r(t(n))))}some(t){let r=!1;return this.iterate(n=>(r=t(n),!r)),r}findFirst(t){let r;return this.iterate(n=>t(n)?(r=n,!1):!0),r}findLast(t){let r;return this.iterate(n=>(t(n)&&(r=n),!0)),r}findLastMaxBy(t){let r,n=!0;return this.iterate(i=>((n||Ft.isGreaterThan(t(i,r)))&&(n=!1,r=i),!0)),r}},I1;function Ni(e,t){const r=Object.create(null);for(const n of e){const i=t(n);let o=r[i];o||(o=r[i]=[]),o.push(n)}return r}var Yo=class{static{I1=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[I1]="SetWithKey";for(const r of e)this.add(r)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(r=>e.call(t,r,r,this))}[Symbol.iterator](){return this.values()}},M1,F1,j1,Li=class{constructor(e,t){this.uri=e,this.value=t}};function Ci(e){return Array.isArray(e)}var U1=class Ve{static{this.c=t=>t.toString()}constructor(t,r){if(this[M1]="ResourceMap",t instanceof Ve)this.d=new Map(t.d),this.e=r??Ve.c;else if(Ci(t)){this.d=new Map,this.e=r??Ve.c;for(const[n,i]of t)this.set(n,i)}else this.d=new Map,this.e=t??Ve.c}set(t,r){return this.d.set(this.e(t),new Li(t,r)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,r){typeof r<"u"&&(t=t.bind(r));for(const[n,i]of this.d)t(i.value,i.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(M1=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},Jo=class{constructor(e,t){this[F1]="ResourceSet",!e||typeof e=="function"?this.c=new U1(e):(this.c=new U1(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((r,n)=>e.call(t,n,n,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(F1=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},z1;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(z1||(z1={}));var Qo=class{constructor(){this[j1]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const r=this.c.get(e);if(r)return t!==0&&this.n(r,t),r.value}set(e,t,r=0){let n=this.c.get(e);if(n)n.value=t,r!==0&&this.n(n,r);else{switch(n={key:e,value:t,next:void 0,previous:void 0},r){case 0:this.l(n);break;case 1:this.k(n);break;case 2:this.l(n);break;default:this.l(n);break}this.c.set(e,n),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const r=this.g;let n=this.d;for(;n;){if(t?e.bind(t)(n.value,n.key,this):e(n.value,n.key,this),this.g!==r)throw new Error("LinkedMap got modified during iteration.");n=n.next}}keys(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:r.key,done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return n}values(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:r.value,done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return n}entries(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:[r.key,r.value],done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return n}[(j1=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.next,r--;this.d=t,this.f=r,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.previous,r--;this.e=t,this.f=r,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,r=e.previous;if(!t||!r)throw new Error("Invalid list");t.previous=r,r.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const r=e.next,n=e.previous;e===this.e?(n.next=void 0,this.e=n):(r.previous=n,n.next=r),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const r=e.next,n=e.previous;e===this.d?(r.previous=void 0,this.d=r):(r.previous=n,n.next=r),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,r)=>{e.push([r,t])}),e}fromJSON(e){this.clear();for(const[t,r]of e)this.set(t,r)}},Ai=class{constructor(){this.c=new Map}add(e,t){let r=this.c.get(e);r||(r=new Set,this.c.set(e,r)),r.add(t)}delete(e,t){const r=this.c.get(e);r&&(r.delete(t),r.size===0&&this.c.delete(e))}forEach(e,t){const r=this.c.get(e);r&&r.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function ki(e,t){const r=this;let n=!1,i;return function(){if(n)return i;if(n=!0,t)try{i=e.apply(r,arguments)}finally{t()}else i=e.apply(r,arguments);return i}}function q1(e){if(!e()){debugger;e(),ue(new B("Assertion Failed"))}}function Si(e){return typeof e=="string"}function $i(e){return!!e&&typeof e[Symbol.iterator]=="function"}function xi(e){return typeof e>"u"}function qe(e){return!Oi(e)}function Oi(e){return xi(e)||e===null}var jt;(function(e){function t(E){return!!E&&typeof E=="object"&&typeof E[Symbol.iterator]=="function"}e.is=t;const r=Object.freeze([]);function n(){return r}e.empty=n;function*i(E){yield E}e.single=i;function o(E){return t(E)?E:i(E)}e.wrap=o;function a(E){return E||r}e.from=a;function*l(E){for(let L=E.length-1;L>=0;L--)yield E[L]}e.reverse=l;function u(E){return!E||E[Symbol.iterator]().next().done===!0}e.isEmpty=u;function c(E){return E[Symbol.iterator]().next().value}e.first=c;function f(E,L){let S=0;for(const V of E)if(L(V,S++))return!0;return!1}e.some=f;function h(E,L){let S=0;for(const V of E)if(!L(V,S++))return!1;return!0}e.every=h;function b(E,L){for(const S of E)if(L(S))return S}e.find=b;function*w(E,L){for(const S of E)L(S)&&(yield S)}e.filter=w;function*k(E,L){let S=0;for(const V of E)yield L(V,S++)}e.map=k;function*T(E,L){let S=0;for(const V of E)yield*L(V,S++)}e.flatMap=T;function*D(...E){for(const L of E)$i(L)?yield*L:yield L}e.concat=D;function q(E,L,S){let V=S;for(const Oe of E)V=L(V,Oe);return V}e.reduce=q;function ge(E){let L=0;for(const S of E)L++;return L}e.length=ge;function*j(E,L,S=E.length){for(L<-E.length&&(L=0),L<0&&(L+=E.length),S<0?S+=E.length:S>E.length&&(S=E.length);L<S;L++)yield E[L]}e.slice=j;function At(E,L=Number.POSITIVE_INFINITY){const S=[];if(L===0)return[S,E];const V=E[Symbol.iterator]();for(let Oe=0;Oe<L;Oe++){const Je=V.next();if(Je.done)return[S,e.empty()];S.push(Je.value)}return[S,{[Symbol.iterator](){return V}}]}e.consume=At;async function kt(E){const L=[];for await(const S of E)L.push(S);return L}e.asyncToArray=kt;async function St(E){let L=[];for await(const S of E)L=L.concat(S);return L}e.asyncToArrayFlat=St})(jt||(jt={}));var _i=!1,Pe=null,Xo=class En{constructor(){this.b=new Map}static{this.a=0}c(t){let r=this.b.get(t);return r||(r={parent:null,source:null,isSingleton:!1,value:t,idx:En.a++},this.b.set(t,r)),r}trackDisposable(t){const r=this.c(t);r.source||(r.source=new Error().stack)}setParent(t,r){const n=this.c(t);n.parent=r}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,r){const n=r.get(t);if(n)return n;const i=t.parent?this.f(this.c(t.parent),r):t;return r.set(t,i),i}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,n])=>n.source!==null&&!this.f(n,t).isSingleton).flatMap(([n])=>n)}computeLeakingDisposables(t=10,r){let n;if(r)n=r;else{const u=new Map,c=[...this.b.values()].filter(h=>h.source!==null&&!this.f(h,u).isSingleton);if(c.length===0)return;const f=new Set(c.map(h=>h.value));if(n=c.filter(h=>!(h.parent&&f.has(h.parent))),n.length===0)throw new Error("There are cyclic diposable chains!")}if(!n)return;function i(u){function c(h,b){for(;h.length>0&&b.some(w=>typeof w=="string"?w===h[0]:h[0].match(w));)h.shift()}const f=u.source.split(`
`).map(h=>h.trim().replace("at ","")).filter(h=>h!=="");return c(f,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),f.reverse()}const o=new Ai;for(const u of n){const c=i(u);for(let f=0;f<=c.length;f++)o.add(c.slice(0,f).join(`
`),u)}n.sort(P1(u=>u.idx,R1));let a="",l=0;for(const u of n.slice(0,t)){l++;const c=i(u),f=[];for(let h=0;h<c.length;h++){let b=c[h];b=`(shared with ${o.get(c.slice(0,h+1).join(`
`)).size}/${n.length} leaks) at ${b}`;const k=o.get(c.slice(0,h).join(`
`)),T=Ni([...k].map(D=>i(D)[h]),D=>D);delete T[c[h]];for(const[D,q]of Object.entries(T))f.unshift(`    - stacktraces of ${q.length} other leaks continue with ${D}`);f.unshift(b)}a+=`


==================== Leaking disposable ${l}/${n.length}: ${u.value.constructor.name} ====================
${f.join(`
`)}
============================================================

`}return n.length>t&&(a+=`


... and ${n.length-t} more leaking disposables

`),{leaks:n,details:a}}};function Ti(e){Pe=e}if(_i){const e="__is_disposable_tracked__";Ti(new class{trackDisposable(t){const r=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(r)},3e3)}setParent(t,r){if(t&&t!==ie.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==ie.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function ct(e){return Pe?.trackDisposable(e),e}function ht(e){Pe?.markAsDisposed(e)}function Ut(e,t){Pe?.setParent(e,t)}function Di(e,t){if(Pe)for(const r of e)Pe.setParent(r,t)}function B1(e){if(jt.is(e)){const t=[];for(const r of e)if(r)try{r.dispose()}catch(n){t.push(n)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Pi(...e){const t=Re(()=>B1(e));return Di(e,t),t}function Re(e){const t=ct({dispose:ki(()=>{ht(t),e()})});return t}var Ee=class Nn{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,ct(this)}dispose(){this.g||(ht(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{B1(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return Ut(t,this),this.g?Nn.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),Ut(t,null))}},ie=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new Ee,ct(this),Ut(this.q,this)}dispose(){ht(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},Ko=class Lt{static{this.Undefined=new Lt(void 0)}constructor(t){this.element=t,this.next=Lt.Undefined,this.prev=Lt.Undefined}},Ri=globalThis.performance.now.bind(globalThis.performance),V1=class Ln{static create(t){return new Ln(t)}constructor(t){this.c=t===!1?Date.now:Ri,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},W1=!1,Ii=!1,ft;(function(e){e.None=()=>ie.None;function t(p){if(Ii){const{onDidAddListener:d}=p,m=zt.create();let g=0;p.onDidAddListener=()=>{++g===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),m.print()),d?.()}}}function r(p,d){return w(p,()=>{},0,void 0,!0,void 0,d)}e.defer=r;function n(p){return(d,m=null,g)=>{let N=!1,A;return A=p($=>{if(!N)return A?A.dispose():N=!0,d.call(m,$)},null,g),N&&A.dispose(),A}}e.once=n;function i(p,d){return e.once(e.filter(p,d))}e.onceIf=i;function o(p,d,m){return h((g,N=null,A)=>p($=>g.call(N,d($)),null,A),m)}e.map=o;function a(p,d,m){return h((g,N=null,A)=>p($=>{d($),g.call(N,$)},null,A),m)}e.forEach=a;function l(p,d,m){return h((g,N=null,A)=>p($=>d($)&&g.call(N,$),null,A),m)}e.filter=l;function u(p){return p}e.signal=u;function c(...p){return(d,m=null,g)=>{const N=Pi(...p.map(A=>A($=>d.call(m,$))));return b(N,g)}}e.any=c;function f(p,d,m,g){let N=m;return o(p,A=>(N=d(N,A),N),g)}e.reduce=f;function h(p,d){let m;const g={onWillAddFirstListener(){m=p(N.fire,N)},onDidRemoveLastListener(){m?.dispose()}};d||t(g);const N=new ee(g);return d?.add(N),N.event}function b(p,d){return d instanceof Array?d.push(p):d&&d.add(p),p}function w(p,d,m=100,g=!1,N=!1,A,$){let U,W,ve,Qe=0,Ue;const f1={leakWarningThreshold:A,onWillAddFirstListener(){U=p(On=>{Qe++,W=d(W,On),g&&!ve&&(Xe.fire(W),W=void 0),Ue=()=>{const _n=W;W=void 0,ve=void 0,(!g||Qe>1)&&Xe.fire(_n),Qe=0},typeof m=="number"?(ve&&clearTimeout(ve),ve=setTimeout(Ue,m)):ve===void 0&&(ve=null,queueMicrotask(Ue))})},onWillRemoveListener(){N&&Qe>0&&Ue?.()},onDidRemoveLastListener(){Ue=void 0,U.dispose()}};$||t(f1);const Xe=new ee(f1);return $?.add(Xe),Xe.event}e.debounce=w;function k(p,d=0,m){return e.debounce(p,(g,N)=>g?(g.push(N),g):[N],d,void 0,!0,void 0,m)}e.accumulate=k;function T(p,d=(g,N)=>g===N,m){let g=!0,N;return l(p,A=>{const $=g||!d(A,N);return g=!1,N=A,$},m)}e.latch=T;function D(p,d,m){return[e.filter(p,d,m),e.filter(p,g=>!d(g),m)]}e.split=D;function q(p,d=!1,m=[],g){let N=m.slice(),A=p(W=>{N?N.push(W):U.fire(W)});g&&g.add(A);const $=()=>{N?.forEach(W=>U.fire(W)),N=null},U=new ee({onWillAddFirstListener(){A||(A=p(W=>U.fire(W)),g&&g.add(A))},onDidAddFirstListener(){N&&(d?setTimeout($):$())},onDidRemoveLastListener(){A&&A.dispose(),A=null}});return g&&g.add(U),U.event}e.buffer=q;function ge(p,d){return(g,N,A)=>{const $=d(new At);return p(function(U){const W=$.evaluate(U);W!==j&&g.call(N,W)},void 0,A)}}e.chain=ge;const j=Symbol("HaltChainable");class At{constructor(){this.f=[]}map(d){return this.f.push(d),this}forEach(d){return this.f.push(m=>(d(m),m)),this}filter(d){return this.f.push(m=>d(m)?m:j),this}reduce(d,m){let g=m;return this.f.push(N=>(g=d(g,N),g)),this}latch(d=(m,g)=>m===g){let m=!0,g;return this.f.push(N=>{const A=m||!d(N,g);return m=!1,g=N,A?N:j}),this}evaluate(d){for(const m of this.f)if(d=m(d),d===j)break;return d}}function kt(p,d,m=g=>g){const g=(...U)=>$.fire(m(...U)),N=()=>p.on(d,g),A=()=>p.removeListener(d,g),$=new ee({onWillAddFirstListener:N,onDidRemoveLastListener:A});return $.event}e.fromNodeEventEmitter=kt;function St(p,d,m=g=>g){const g=(...U)=>$.fire(m(...U)),N=()=>p.addEventListener(d,g),A=()=>p.removeEventListener(d,g),$=new ee({onWillAddFirstListener:N,onDidRemoveLastListener:A});return $.event}e.fromDOMEventEmitter=St;function E(p,d){let m;const g=new Promise((N,A)=>{const $=n(p)(N,null,d);m=()=>$.dispose()});return g.cancel=m,g}e.toPromise=E;function L(p){const d=new ee;return p.then(m=>{d.fire(m)},()=>{d.fire(void 0)}).finally(()=>{d.dispose()}),d.event}e.fromPromise=L;function S(p,d){return p(m=>d.fire(m))}e.forward=S;function V(p,d,m){return d(m),p(g=>d(g))}e.runAndSubscribe=V;class Oe{constructor(d,m){this._observable=d,this.f=0,this.g=!1;const g={onWillAddFirstListener:()=>{d.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{d.removeObserver(this)}};m||t(g),this.emitter=new ee(g),m&&m.add(this.emitter)}beginUpdate(d){this.f++}handlePossibleChange(d){}handleChange(d,m){this.g=!0}endUpdate(d){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function Je(p,d){return new Oe(p,d).emitter.event}e.fromObservable=Je;function xn(p){return(d,m,g)=>{let N=0,A=!1;const $={beginUpdate(){N++},endUpdate(){N--,N===0&&(p.reportChanges(),A&&(A=!1,d.call(m)))},handlePossibleChange(){},handleChange(){A=!0}};p.addObserver($),p.reportChanges();const U={dispose(){p.removeObserver($)}};return g instanceof Ee?g.add(U):Array.isArray(g)&&g.push(U),U}}e.fromObservableLight=xn})(ft||(ft={}));var Mi=class l1{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${l1.f++}`,l1.all.add(this)}start(t){this.g=new V1,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},G1=-1,Fi=class Cn{static{this.f=1}constructor(t,r,n=(Cn.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=r,this.name=n,this.h=0}dispose(){this.g?.clear()}check(t,r){const n=this.threshold;if(n<=0||r<n)return;this.g||(this.g=new Map);const i=this.g.get(t.value)||0;if(this.g.set(t.value,i+1),this.h-=1,this.h<=0){this.h=n*.5;const[o,a]=this.getMostFrequentStack(),l=`[${this.name}] potential listener LEAK detected, having ${r} listeners already. MOST frequent listener (${a}):`;console.warn(l),console.warn(o);const u=new ji(l,o);this.j(u)}return()=>{const o=this.g.get(t.value)||0;this.g.set(t.value,o-1)}}getMostFrequentStack(){if(!this.g)return;let t,r=0;for(const[n,i]of this.g)(!t||r<i)&&(t=[n,i],r=i);return t}},zt=class An{static create(){const t=new Error;return new An(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},ji=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},Ui=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},zi=0,dt=class{constructor(e){this.value=e,this.id=zi++}},qi=2,Bi=(e,t)=>{if(e instanceof dt)t(e);else for(let r=0;r<e.length;r++){const n=e[r];n&&t(n)}},ee=class{constructor(e){this.A=0,this.g=e,this.j=G1>0||this.g?.leakWarningThreshold?new Fi(e?.onListenerError??ue,this.g?.leakWarningThreshold??G1):void 0,this.m=this.g?._profName?new Mi(this.g._profName):void 0,this.z=this.g?.deliveryQueue}dispose(){if(!this.q){if(this.q=!0,this.z?.current===this&&this.z.reset(),this.w){if(W1){const e=this.w;queueMicrotask(()=>{Bi(e,t=>t.stack?.print())})}this.w=void 0,this.A=0}this.g?.onDidRemoveLastListener?.(),this.j?.dispose()}}get event(){return this.u??=(e,t,r)=>{if(this.j&&this.A>this.j.threshold**2){const l=`[${this.j.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.A} vs ${this.j.threshold})`;console.warn(l);const u=this.j.getMostFrequentStack()??["UNKNOWN stack",-1],c=new Ui(`${l}. HINT: Stack shows most frequent listener (${u[1]}-times)`,u[0]);return(this.g?.onListenerError||ue)(c),ie.None}if(this.q)return ie.None;t&&(e=e.bind(t));const n=new dt(e);let i,o;this.j&&this.A>=Math.ceil(this.j.threshold*.2)&&(n.stack=zt.create(),i=this.j.check(n.stack,this.A+1)),W1&&(n.stack=o??zt.create()),this.w?this.w instanceof dt?(this.z??=new Vi,this.w=[this.w,n]):this.w.push(n):(this.g?.onWillAddFirstListener?.(this),this.w=n,this.g?.onDidAddFirstListener?.(this)),this.g?.onDidAddListener?.(this),this.A++;const a=Re(()=>{i?.(),this.B(n)});return r instanceof Ee?r.add(a):Array.isArray(r)&&r.push(a),a},this.u}B(e){if(this.g?.onWillRemoveListener?.(this),!this.w)return;if(this.A===1){this.w=void 0,this.g?.onDidRemoveLastListener?.(this),this.A=0;return}const t=this.w,r=t.indexOf(e);if(r===-1)throw console.log("disposed?",this.q),console.log("size?",this.A),console.log("arr?",JSON.stringify(this.w)),new Error("Attempted to dispose unknown listener");this.A--,t[r]=void 0;const n=this.z.current===this;if(this.A*qi<=t.length){let i=0;for(let o=0;o<t.length;o++)t[o]?t[i++]=t[o]:n&&i<this.z.end&&(this.z.end--,i<this.z.i&&this.z.i--);t.length=i}}C(e,t){if(!e)return;const r=this.g?.onListenerError||ue;if(!r){e.value(t);return}try{e.value(t)}catch(n){r(n)}}D(e){const t=e.current.w;for(;e.i<e.end;)this.C(t[e.i++],e.value);e.reset()}fire(e){if(this.z?.current&&(this.D(this.z),this.m?.stop()),this.m?.start(this.A),this.w)if(this.w instanceof dt)this.C(this.w,e);else{const t=this.z;t.enqueue(this,e,this.w.length),this.D(t)}this.m?.stop()}hasListeners(){return this.A>0}},Vi=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,r){this.i=0,this.end=r,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}},H1=Object.freeze(function(e,t){const r=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(r)}}}),Z1;(function(e){function t(r){return r===e.None||r===e.Cancelled||r instanceof Wi?!0:!r||typeof r!="object"?!1:typeof r.isCancellationRequested=="boolean"&&typeof r.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:ft.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:H1})})(Z1||(Z1={}));var Wi=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?H1:(this.b||(this.b=new ee),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}};function Gi(e){return e}var Hi=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=Gi):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},qt=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function Zi(e){return e.split(/\r\n|\r|\n/)}function Yi(e,t){return e<t?-1:e>t?1:0}function Ji(e,t,r=0,n=e.length,i=0,o=t.length){for(;r<n&&i<o;r++,i++){const u=e.charCodeAt(r),c=t.charCodeAt(i);if(u<c)return-1;if(u>c)return 1}const a=n-r,l=o-i;return a<l?-1:a>l?1:0}function Y1(e,t,r=0,n=e.length,i=0,o=t.length){for(;r<n&&i<o;r++,i++){let u=e.charCodeAt(r),c=t.charCodeAt(i);if(u===c)continue;if(u>=128||c>=128)return Ji(e.toLowerCase(),t.toLowerCase(),r,n,i,o);J1(u)&&(u-=32),J1(c)&&(c-=32);const f=u-c;if(f!==0)return f}const a=n-r,l=o-i;return a<l?-1:a>l?1:0}function J1(e){return e>=97&&e<=122}function Q1(e){return e>=65&&e<=90}function Qi(e,t){return e.length===t.length&&Y1(e,t)===0}function Xi(e,t){const r=t.length;return t.length>e.length?!1:Y1(e,t,0,r)===0}var Ki=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,es=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,ts=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,e0=new RegExp("(?:"+[Ki.source,es.source,ts.source].join("|")+")","g"),t0="\uFEFF",X1;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(X1||(X1={}));var r0=class We{static{this.c=null}static getInstance(){return We.c||(We.c=new We),We.c}constructor(){this.d=rs()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const r=this.d,n=r.length/3;let i=1;for(;i<=n;)if(t<r[3*i])i=2*i;else if(t>r[3*i+1])i=2*i+1;else return r[3*i+2];return 0}};function rs(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var K1;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(K1||(K1={}));var n0=class Ge{static{this.c=new qt(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new Hi({getCacheKey:JSON.stringify},t=>{function r(f){const h=new Map;for(let b=0;b<f.length;b+=2)h.set(f[b],f[b+1]);return h}function n(f,h){const b=new Map(f);for(const[w,k]of h)b.set(w,k);return b}function i(f,h){if(!f)return h;const b=new Map;for(const[w,k]of f)h.has(w)&&b.set(w,k);return b}const o=this.c.value;let a=t.filter(f=>!f.startsWith("_")&&f in o);a.length===0&&(a=["_default"]);let l;for(const f of a){const h=r(o[f]);l=i(l,h)}const u=r(o._common),c=n(u,l);return new Ge(c)})}static getInstance(t){return Ge.d.get(Array.from(t))}static{this.e=new qt(()=>Object.keys(Ge.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return Ge.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let r=0;r<t.length;r++){const n=t.codePointAt(r);if(typeof n=="number"&&this.isAmbiguous(n))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},i0=class He{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(He.c())].flat())),this.d}static isInvisibleCharacter(t){return He.e().has(t)}static containsInvisibleCharacter(t){for(let r=0;r<t.length;r++){const n=t.codePointAt(r);if(typeof n=="number"&&(He.isInvisibleCharacter(n)||n===32))return!0}return!1}static get codePoints(){return He.e()}};function me(e){return e===47||e===92}function er(e){return e.replace(/[\\/]/g,O.sep)}function ns(e){return e.indexOf("/")===-1&&(e=er(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function tr(e,t=O.sep){if(!e)return"";const r=e.length,n=e.charCodeAt(0);if(me(n)){if(me(e.charCodeAt(1))&&!me(e.charCodeAt(2))){let o=3;const a=o;for(;o<r&&!me(e.charCodeAt(o));o++);if(a!==o&&!me(e.charCodeAt(o+1))){for(o+=1;o<r;o++)if(me(e.charCodeAt(o)))return e.slice(0,o+1).replace(/[\\/]/g,t)}}return t}else if(is(n)&&e.charCodeAt(1)===58)return me(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let i=e.indexOf("://");if(i!==-1){for(i+=3;i<r;i++)if(me(e.charCodeAt(i)))return e.slice(0,i+1)}return""}function rr(e,t,r,n=st){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(r){if(!Xi(e,t))return!1;if(t.length===e.length)return!0;let o=t.length;return t.charAt(t.length-1)===n&&o--,e.charAt(o)===n}return t.charAt(t.length-1)!==n&&(t+=n),e.indexOf(t)===0}function is(e){return e>=65&&e<=90||e>=97&&e<=122}var M;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.vscodeChatInput="chatSessionInput",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(M||(M={}));var ss="tkn",os=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=O.join(t??"/",ls(e))}getServerRootPath(){return this.f}get g(){return O.join(this.f,M.vscodeRemoteResource)}set(e,t,r){this.a[e]=t,this.b[e]=r}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(a){return ue(a),e}const t=e.authority;let r=this.a[t];r&&r.indexOf(":")!==-1&&r.indexOf("[")===-1&&(r=`[${r}]`);const n=this.b[t],i=this.c[t];let o=`path=${encodeURIComponent(e.path)}`;return typeof i=="string"&&(o+=`&${ss}=${encodeURIComponent(i)}`),J.from({scheme:w1?this.d:M.vscodeRemoteResource,authority:`${r}:${n}`,path:this.g,query:o})}},as=new os;function ls(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var us="vs/../../node_modules",cs="vs/../../node_modules.asar",nr="vscode-app",hs=class Ct{static{this.a=nr}asBrowserUri(t){const r=this.b(t);return this.uriToBrowserUri(r)}uriToBrowserUri(t){return t.scheme===M.vscodeRemote?as.rewrite(t):t.scheme===M.file&&(Gn||Zn===`${M.vscodeFileResource}://${Ct.a}`)?t.with({scheme:M.vscodeFileResource,authority:t.authority||Ct.a,query:null,fragment:null}):t}asFileUri(t){const r=this.b(t);return this.uriToFileUri(r)}uriToFileUri(t){return t.scheme===M.vscodeFileResource?t.with({scheme:M.file,authority:t.authority!==Ct.a?t.authority:null,query:null,fragment:null}):t}b(t){if(J.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const r=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(r))return J.joinPath(J.parse(r,!0),t);const n=ai(r,t);return J.file(n)}throw new Error("Cannot determine URI for module id!")}},fs=new hs,s0=Object.freeze({"Cache-Control":"no-cache, no-store"}),o0=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),ir;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const r="vscode-coi";function n(o){let a;typeof o=="string"?a=new URL(o).searchParams:o instanceof URL?a=o.searchParams:J.isUri(o)&&(a=new URL(o.toString(!0)).searchParams);const l=a?.get(r);if(l)return t.get(l)}e.getHeadersFromQuery=n;function i(o,a,l){if(!globalThis.crossOriginIsolated)return;const u=a&&l?"3":l?"2":"1";o instanceof URLSearchParams?o.set(r,u):o[r]=u}e.addSearchParam=i})(ir||(ir={}));function ce(e){return ot(e,!0)}var Bt=class{constructor(e){this.a=e}compare(e,t,r=!1){return e===t?0:Yi(this.getComparisonKey(e,r),this.getComparisonKey(t,r))}isEqual(e,t,r=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,r)===this.getComparisonKey(t,r)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,r=!1){if(e.scheme===t.scheme){if(e.scheme===M.file)return rr(ce(e),ce(t),this.a(e))&&e.query===t.query&&(r||e.fragment===t.fragment);if(sr(e.authority,t.authority))return rr(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(r||e.fragment===t.fragment)}return!1}joinPath(e,...t){return J.joinPath(e,...t)}basenameOrAuthority(e){return gs(e)||e.authority}basename(e){return O.basename(e.path)}extname(e){return O.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===M.file?t=J.file(ci(ce(e))).path:(t=O.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===M.file?t=J.file(oi(ce(e))).path:t=O.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!sr(e.authority,t.authority))return;if(e.scheme===M.file){const i=ui(ce(e),ce(t));return Te?er(i):i}let r=e.path||"/";const n=t.path||"/";if(this.a(e)){let i=0;for(const o=Math.min(r.length,n.length);i<o&&!(r.charCodeAt(i)!==n.charCodeAt(i)&&r.charAt(i).toLowerCase()!==n.charAt(i).toLowerCase());i++);r=n.substr(0,i)+r.substr(i)}return O.relative(r,n)}resolvePath(e,t){if(e.scheme===M.file){const r=J.file(li(ce(e),t));return e.with({authority:r.authority,path:r.path})}return t=ns(t),e.with({path:O.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&Qi(e,t)}hasTrailingPathSeparator(e,t=st){if(e.scheme===M.file){const r=ce(e);return r.length>tr(r).length&&r[r.length-1]===t}else{const r=e.path;return r.length>1&&r.charCodeAt(r.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=st){return or(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=st){let r=!1;if(e.scheme===M.file){const n=ce(e);r=n!==void 0&&n.length===tr(n).length&&n[n.length-1]===t}else{t="/";const n=e.path;r=n.length===1&&n.charCodeAt(n.length-1)===47}return!r&&!or(e,t)?e.with({path:e.path+"/"}):e}},x=new Bt(()=>!1),a0=new Bt(e=>e.scheme===M.file?!Wn:!0),l0=new Bt(e=>!0),ds=x.isEqual.bind(x),u0=x.isEqualOrParent.bind(x),c0=x.getComparisonKey.bind(x),h0=x.basenameOrAuthority.bind(x),gs=x.basename.bind(x),f0=x.extname.bind(x),d0=x.dirname.bind(x),g0=x.joinPath.bind(x),b0=x.normalizePath.bind(x),m0=x.relativePath.bind(x),p0=x.resolvePath.bind(x),v0=x.isAbsolutePath.bind(x),sr=x.isEqualAuthority.bind(x),or=x.hasTrailingPathSeparator.bind(x),w0=x.removeTrailingPathSeparator.bind(x),y0=x.addTrailingPathSeparator.bind(x),ar;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(r){const n=new Map;r.path.substring(r.path.indexOf(";")+1,r.path.lastIndexOf(";")).split(";").forEach(a=>{const[l,u]=a.split(":");l&&u&&n.set(l,u)});const o=r.path.substring(0,r.path.indexOf(";"));return o&&n.set(e.META_DATA_MIME,o),n}e.parseMetaData=t})(ar||(ar={}));var bs=class{constructor(){this.a=Object.create(null)}reset(){this.a=Object.create(null)}register(e){if(this.a[e.scopeName]){const t=this.a[e.scopeName];ds(t.location,e.location)||console.warn(`Overwriting grammar scope name to file mapping for scope ${e.scopeName}.
Old grammar file: ${t.location.toString()}.
New grammar file: ${e.location.toString()}`)}this.a[e.scopeName]=e}getGrammarDefinition(e){return this.a[e]||null}},Vt="No TM Grammar registered for this language.",ms=class extends ie{constructor(e,t,r,n){super(),this.a=e,this.b=r.INITIAL,this.c=new bs,this.f={},this.g={},this.h=new Map,this.j=this.B(new r.Registry({onigLib:n,loadGrammar:async i=>{const o=this.c.getGrammarDefinition(i);if(!o)return this.a.logTrace(`No grammar found for scope ${i}`),null;const a=o.location;try{const l=await this.a.readFile(a);return r.parseRawGrammar(l,a.path)}catch(l){return this.a.logError(`Unable to load and parse grammar for scope ${i} from ${a}`,l),null}},getInjections:i=>{const o=i.split(".");let a=[];for(let l=1;l<=o.length;l++){const u=o.slice(0,l).join(".");a=[...a,...this.f[u]||[]]}return a}}));for(const i of t){if(this.c.register(i),i.injectTo){for(const o of i.injectTo){let a=this.f[o];a||(this.f[o]=a=[]),a.push(i.scopeName)}if(i.embeddedLanguages)for(const o of i.injectTo){let a=this.g[o];a||(this.g[o]=a=[]),a.push(i.embeddedLanguages)}}i.language&&this.h.set(i.language,i.scopeName)}}has(e){return this.h.has(e)}setTheme(e,t){this.j.setTheme(e,t)}getColorMap(){return this.j.getColorMap()}async createGrammar(e,t){const r=this.h.get(e);if(typeof r!="string")throw new Error(Vt);const n=this.c.getGrammarDefinition(r);if(!n)throw new Error(Vt);const i=n.embeddedLanguages;if(this.g[r]){const l=this.g[r];for(const u of l)for(const c of Object.keys(u))i[c]=u[c]}const o=Object.keys(i).length>0;let a;try{a=await this.j.loadGrammarWithConfiguration(r,t,{embeddedLanguages:i,tokenTypes:n.tokenTypes,balancedBracketSelectors:n.balancedBracketSelectors,unbalancedBracketSelectors:n.unbalancedBracketSelectors})}catch(l){throw l.message&&l.message.startsWith("No grammar provided for")?new Error(Vt):l}return{languageId:e,grammar:a,initialState:this.b,containsEmbeddedLanguages:o,sourceExtensionId:n.sourceExtensionId}}},ps=function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID.bind(crypto);const e=new Uint8Array(16),t=[];for(let r=0;r<256;r++)t.push(r.toString(16).padStart(2,"0"));return function(){crypto.getRandomValues(e),e[6]=e[6]&15|64,e[8]=e[8]&63|128;let n=0,i="";return i+=t[e[n++]],i+=t[e[n++]],i+=t[e[n++]],i+=t[e[n++]],i+="-",i+=t[e[n++]],i+=t[e[n++]],i+="-",i+=t[e[n++]],i+=t[e[n++]],i+="-",i+=t[e[n++]],i+=t[e[n++]],i+="-",i+=t[e[n++]],i+=t[e[n++]],i+=t[e[n++]],i+=t[e[n++]],i+=t[e[n++]],i+=t[e[n++]],i}}(),vs=!1,ws=class{constructor(e,t,r){this.id=e,this.dependencies=t,this.callback=r}},Ne;(function(e){e[e.Uninitialized=1]="Uninitialized",e[e.InitializedInternal=2]="InitializedInternal",e[e.InitializedExternal=3]="InitializedExternal"})(Ne||(Ne={}));var ys=class kn{static{this.INSTANCE=new kn}constructor(){this.a=typeof self=="object"&&self.constructor&&self.constructor.name==="DedicatedWorkerGlobalScope",this.b=typeof document=="object",this.c=[],this.d=Ne.Uninitialized}g(){if(this.d===Ne.Uninitialized){if(globalThis.define){this.d=Ne.InitializedExternal;return}}else return;this.d=Ne.InitializedInternal,globalThis.define=(t,r,n)=>{typeof t!="string"&&(n=r,r=t,t=null),(typeof r!="object"||!Array.isArray(r))&&(n=r,r=null),this.c.push(new ws(t,r,n))},globalThis.define.amd=!0,this.b?this.f=globalThis._VSCODE_WEB_PACKAGE_TTP??window.trustedTypes?.createPolicy("amdLoader",{createScriptURL(t){if(t.startsWith(window.location.origin)||t.startsWith(`${M.vscodeFileResource}://${nr}`))return t;throw new Error(`[trusted_script_src] Invalid script url: ${t}`)}}):this.a&&(this.f=globalThis._VSCODE_WEB_PACKAGE_TTP??globalThis.trustedTypes?.createPolicy("amdLoader",{createScriptURL(t){return t}}))}async load(t){if(this.g(),this.d===Ne.InitializedExternal)return new Promise(a=>{const l=ps();globalThis.define(l,[t],function(u){a(u)})});const r=await(this.a?this.i(t):this.b?this.h(t):this.j(t));if(!r){console.warn(`Did not receive a define call from script ${t}`);return}const n={},i=[],o=[];if(Array.isArray(r.dependencies))for(const a of r.dependencies)a==="exports"?i.push(n):o.push(a);if(o.length>0)throw new Error(`Cannot resolve dependencies for script ${t}. The dependencies are: ${o.join(", ")}`);return typeof r.callback=="function"?r.callback(...i)??n:r.callback}h(t){return new Promise((r,n)=>{const i=document.createElement("script");i.setAttribute("async","async"),i.setAttribute("type","text/javascript");const o=()=>{i.removeEventListener("load",a),i.removeEventListener("error",l)},a=u=>{o(),r(this.c.pop())},l=u=>{o(),n(u)};i.addEventListener("load",a),i.addEventListener("error",l),this.f&&(t=this.f.createScriptURL(t)),i.setAttribute("src",t),window.document.getElementsByTagName("head")[0].appendChild(i)})}async i(t){return this.f&&(t=this.f.createScriptURL(t)),await import(t),this.c.pop()}async j(t){try{const r=(await import("fs")).default,n=(await import("vm")).default,i=(await import("module")).default,o=J.parse(t).fsPath,a=r.readFileSync(o).toString(),l=i.wrap(a.replace(/^#!.*/,""));return new n.Script(l).runInThisContext().apply(),this.c.pop()}catch(r){throw r}}},Wt=new Map;async function Gt(e,t,r){r===void 0&&(r=!!(globalThis._VSCODE_PRODUCT_JSON??globalThis.vscode?.context?.configuration()?.product)?.commit);const n=t?`${e}/${t}`:e;if(Wt.has(n))return Wt.get(n);let i;if(/^\w[\w\d+.-]*:\/\//.test(n))i=n;else{const u=`${vs&&r&&!w1?cs:us}/${n}`;i=fs.asBrowserUri(u).toString(!0)}const o=ys.INSTANCE.load(i);return Wt.set(n,o),o}var E0=Symbol("MicrotaskDelay"),Es=class{constructor(e,t){this.b=void 0,this.a=e,this.d=t,this.f=this.g.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearTimeout(this.b),this.b=void 0)}schedule(e=this.d){this.cancel(),this.b=setTimeout(this.f,e)}get delay(){return this.d}set delay(e){this.d=e}isScheduled(){return this.b!==void 0}flush(){this.isScheduled()&&(this.cancel(),this.h())}g(){this.b=void 0,this.a&&this.h()}h(){this.a?.()}},Ns,Ht;(function(){const e=globalThis;typeof e.requestIdleCallback!="function"||typeof e.cancelIdleCallback!="function"?Ht=(t,r,n)=>{E1(()=>{if(i)return;const o=Date.now()+15;r(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,o-Date.now())}}))});let i=!1;return{dispose(){i||(i=!0)}}}:Ht=(t,r,n)=>{const i=t.requestIdleCallback(r,typeof n=="number"?{timeout:n}:void 0);let o=!1;return{dispose(){o||(o=!0,t.cancelIdleCallback(i))}}},Ns=(t,r)=>Ht(globalThis,t,r)})();var lr;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(lr||(lr={}));var ur;(function(e){async function t(n){let i;const o=await Promise.all(n.map(a=>a.then(l=>l,l=>{i||(i=l)})));if(typeof i<"u")throw i;return o}e.settled=t;function r(n){return new Promise(async(i,o)=>{try{await n(i,o)}catch(a){o(a)}})}e.withAsyncBody=r})(ur||(ur={}));var cr;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(cr||(cr={}));var N0=class X{static fromArray(t){return new X(r=>{r.emitMany(t)})}static fromPromise(t){return new X(async r=>{r.emitMany(await t)})}static fromPromisesResolveOrder(t){return new X(async r=>{await Promise.all(t.map(async n=>r.emitOne(await n)))})}static merge(t){return new X(async r=>{await Promise.all(t.map(async n=>{for await(const i of n)r.emitOne(i)}))})}static{this.EMPTY=X.fromArray([])}constructor(t,r){this.a=0,this.b=[],this.d=null,this.f=r,this.g=new ee,queueMicrotask(async()=>{const n={emitOne:i=>this.h(i),emitMany:i=>this.j(i),reject:i=>this.l(i)};try{await Promise.resolve(t(n)),this.k()}catch(i){this.l(i)}finally{n.emitOne=void 0,n.emitMany=void 0,n.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await ft.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,r){return new X(async n=>{for await(const i of t)n.emitOne(r(i))})}map(t){return X.map(this,t)}static filter(t,r){return new X(async n=>{for await(const i of t)r(i)&&n.emitOne(i)})}filter(t){return X.filter(this,t)}static coalesce(t){return X.filter(t,r=>!!r)}coalesce(){return X.coalesce(this)}static async toPromise(t){const r=[];for await(const n of t)r.push(n);return r}toPromise(){return X.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},gt=class{constructor(e,t,r){this.owner=e,this.debugNameSource=t,this.referenceFn=r}getDebugName(e){return Ls(e,this)}},hr=new Map,Zt=new WeakMap;function Ls(e,t){const r=Zt.get(e);if(r)return r;const n=Cs(e,t);if(n){let i=hr.get(n)??0;i++,hr.set(n,i);const o=i===1?n:`${n}#${i}`;return Zt.set(e,o),o}}function Cs(e,t){const r=Zt.get(e);if(r)return r;const n=t.owner?ks(t.owner)+".":"";let i;const o=t.debugNameSource;if(o!==void 0)if(typeof o=="function"){if(i=o(),i!==void 0)return n+i}else return n+o;const a=t.referenceFn;if(a!==void 0&&(i=Yt(a),i!==void 0))return n+i;if(t.owner!==void 0){const l=As(t.owner,e);if(l!==void 0)return n+l}}function As(e,t){for(const r in e)if(e[r]===t)return r}var fr=new Map,dr=new WeakMap;function ks(e){const t=dr.get(e);if(t)return t;const r=gr(e)??"Object";let n=fr.get(r)??0;n++,fr.set(r,n);const i=n===1?r:`${r}#${n}`;return dr.set(e,i),i}function gr(e){const t=e.constructor;if(t)return t.name==="Object"?void 0:t.name}function Yt(e){const t=e.toString(),n=/\/\*\*\s*@description\s*([^*]*)\*\//.exec(t);return(n?n[1]:void 0)?.trim()}var Jt=(e,t)=>e===t;function br(e){const t=new Error("BugIndicatingErrorRecovery: "+e);ue(t),console.error("recovered from an error that indicates a bug",t)}var Le;function Qt(e){Le?Le instanceof mr?Le.loggers.push(e):Le=new mr([Le,e]):Le=e}function F(){return Le}var Xt=void 0;function Ss(e){Xt=e}function $s(e){Xt&&Xt(e)}var mr=class{constructor(e){this.loggers=e}handleObservableCreated(e){for(const t of this.loggers)t.handleObservableCreated(e)}handleOnListenerCountChanged(e,t){for(const r of this.loggers)r.handleOnListenerCountChanged(e,t)}handleObservableUpdated(e,t){for(const r of this.loggers)r.handleObservableUpdated(e,t)}handleAutorunCreated(e){for(const t of this.loggers)t.handleAutorunCreated(e)}handleAutorunDisposed(e){for(const t of this.loggers)t.handleAutorunDisposed(e)}handleAutorunDependencyChanged(e,t,r){for(const n of this.loggers)n.handleAutorunDependencyChanged(e,t,r)}handleAutorunStarted(e){for(const t of this.loggers)t.handleAutorunStarted(e)}handleAutorunFinished(e){for(const t of this.loggers)t.handleAutorunFinished(e)}handleDerivedDependencyChanged(e,t,r){for(const n of this.loggers)n.handleDerivedDependencyChanged(e,t,r)}handleDerivedCleared(e){for(const t of this.loggers)t.handleDerivedCleared(e)}handleBeginTransaction(e){for(const t of this.loggers)t.handleBeginTransaction(e)}handleEndTransaction(e){for(const t of this.loggers)t.handleEndTransaction(e)}};function pr(e,t){const r=new vr(e,t);try{e(r)}finally{r.finish()}}function xs(e,t,r){e?t(e):pr(t,r)}var vr=class{constructor(e,t){this._fn=e,this.b=t,this.a=[],F()?.handleBeginTransaction(this)}getDebugName(){return this.b?this.b():Yt(this._fn)}updateObserver(e,t){if(!this.a){br("Transaction already finished!"),pr(r=>{r.updateObserver(e,t)});return}this.a.push({observer:e,observable:t}),e.beginUpdate(t)}finish(){const e=this.a;if(!e){br("transaction.finish() has already been called!");return}for(let t=0;t<e.length;t++){const{observer:r,observable:n}=e[t];r.endUpdate(n)}this.a=null,F()?.handleEndTransaction(this)}debugGetUpdatingObservers(){return this.a}},Kt;function Os(e){Kt=e}var wr;function _s(e){wr=e}var yr;function Ts(e){yr=e}var Ds=class{get TChange(){return null}reportChanges(){this.get()}read(e){return e?e.readObservable(this):this.get()}map(e,t){const r=t===void 0?void 0:e,n=t===void 0?e:t;return Kt({owner:r,debugName:()=>{const i=Yt(n);if(i!==void 0)return i;const a=/^\s*\(?\s*([a-zA-Z_$][a-zA-Z_$0-9]*)\s*\)?\s*=>\s*\1(?:\??)\.([a-zA-Z_$][a-zA-Z_$0-9]*)\s*$/.exec(n.toString());if(a)return`${this.debugName}.${a[2]}`;if(!r)return`${this.debugName} (mapped)`},debugReferenceFn:n},i=>n(this.read(i),i))}flatten(){return Kt({owner:void 0,debugName:()=>`${this.debugName} (flattened)`},e=>this.read(e).read(e))}recomputeInitiallyAndOnChange(e,t){return e.add(wr(this,t)),this}keepObserved(e){return e.add(yr(this)),this}get b(){return this.get()}},e1=class extends Ds{constructor(){super(),this.f=new Set,F()?.handleObservableCreated(this)}addObserver(e){const t=this.f.size;this.f.add(e),t===0&&this.g(),t!==this.f.size&&F()?.handleOnListenerCountChanged(this,this.f.size)}removeObserver(e){const t=this.f.delete(e);t&&this.f.size===0&&this.h(),t&&F()?.handleOnListenerCountChanged(this,this.f.size)}g(){}h(){}log(){const e=!!F();return $s(this),e||F()?.handleObservableCreated(this),this}debugGetObservers(){return this.f}};function Ps(e,t){let r;return typeof e=="string"?r=new gt(void 0,e,void 0):r=new gt(e,void 0,void 0),new t1(r,t,Jt)}var t1=class extends e1{get debugName(){return this.c.getDebugName(this)??"ObservableValue"}constructor(e,t,r){super(),this.c=e,this.d=r,this.a=t,F()?.handleObservableUpdated(this,{hadValue:!1,newValue:t,change:void 0,didChange:!0,oldValue:void 0})}get(){return this.a}set(e,t,r){if(r===void 0&&this.d(this.a,e))return;let n;t||(t=n=new vr(()=>{},()=>`Setting ${this.debugName}`));try{const i=this.a;this.i(e),F()?.handleObservableUpdated(this,{oldValue:i,newValue:e,change:r,didChange:!0,hadValue:!0});for(const o of this.f)t.updateObserver(o,this),o.handleChange(this,r)}finally{n&&n.finish()}}toString(){return`${this.debugName}: ${this.a}`}i(e){this.a=e}debugGetState(){return{value:this.a}}debugSetValue(e){this.a=e}},Er;(function(e){e[e.dependenciesMightHaveChanged=1]="dependenciesMightHaveChanged",e[e.stale=2]="stale",e[e.upToDate=3]="upToDate"})(Er||(Er={}));var Rs=class{get debugName(){return this._debugNameData.getDebugName(this)??"(anonymous)"}constructor(e,t,r){this._debugNameData=e,this._runFn=t,this.j=r,this.a=2,this.b=0,this.c=!1,this.f=new Set,this.g=new Set,this.i=!1,this.n=void 0,this.p=void 0,this.h=this.j?.createChangeSummary(void 0),F()?.handleAutorunCreated(this),this.k(),ct(this)}dispose(){if(!this.c){this.c=!0;for(const e of this.f)e.removeObserver(this);this.f.clear(),this.n!==void 0&&this.n.dispose(),this.p!==void 0&&this.p.dispose(),F()?.handleAutorunDisposed(this),ht(this)}}k(){const e=this.g;this.g=this.f,this.f=e,this.a=3;try{if(!this.c){F()?.handleAutorunStarted(this);const t=this.h,r=this.p;r!==void 0&&(this.p=void 0);try{this.i=!0,this.j&&(this.j.beforeUpdate?.(this,t),this.h=this.j.createChangeSummary(t)),this.n!==void 0&&(this.n.dispose(),this.n=void 0),this._runFn(this,t)}catch(n){ut(n)}finally{this.i=!1,r!==void 0&&r.dispose()}}}finally{this.c||F()?.handleAutorunFinished(this);for(const t of this.g)t.removeObserver(this);this.g.clear()}}toString(){return`Autorun<${this.debugName}>`}beginUpdate(e){this.a===3&&(this.a=1),this.b++}endUpdate(e){try{if(this.b===1)do{if(this.a===1){this.a=3;for(const t of this.f)if(t.reportChanges(),this.a===2)break}this.a!==3&&this.k()}while(this.a!==3)}finally{this.b--}q1(()=>this.b>=0)}handlePossibleChange(e){this.a===3&&this.l(e)&&(this.a=1)}handleChange(e,t){if(this.l(e)){F()?.handleAutorunDependencyChanged(this,e,t);try{(this.j?this.j.handleChange({changedObservable:e,change:t,didChange:n=>n===e},this.h):!0)&&(this.a=2)}catch(r){ut(r)}}}l(e){return this.f.has(e)&&!this.g.has(e)}m(){if(!this.i)throw new B("The reader object cannot be used outside its compute function!")}readObservable(e){if(this.m(),this.c)return e.get();e.addObserver(this);const t=e.get();return this.f.add(e),this.g.delete(e),t}get store(){if(this.m(),this.c)throw new B("Cannot access store after dispose");return this.n===void 0&&(this.n=new Ee),this.n}get delayedStore(){if(this.m(),this.c)throw new B("Cannot access store after dispose");return this.p===void 0&&(this.p=new Ee),this.p}debugGetState(){return{isRunning:this.i,updateCount:this.b,dependencies:this.f,state:this.a}}debugRerun(){this.i?this.a=2:this.k()}},Nr;(function(e){e[e.initial=0]="initial",e[e.dependenciesMightHaveChanged=1]="dependenciesMightHaveChanged",e[e.stale=2]="stale",e[e.upToDate=3]="upToDate"})(Nr||(Nr={}));var he=class extends e1{get debugName(){return this._debugNameData.getDebugName(this)??"(anonymous)"}constructor(e,t,r,n=void 0,i){super(),this._debugNameData=e,this._computeFn=t,this.w=r,this.x=n,this.y=i,this.a=0,this.c=void 0,this.i=0,this.j=new Set,this.k=new Set,this.l=void 0,this.m=!1,this.n=!1,this.p=!1,this.q=!1,this.s=!1,this.t=void 0,this.u=void 0,this.v=null,this.l=this.w?.createChangeSummary(void 0)}h(){this.a=0,this.c=void 0,F()?.handleDerivedCleared(this);for(const e of this.j)e.removeObserver(this);this.j.clear(),this.t!==void 0&&(this.t.dispose(),this.t=void 0),this.u!==void 0&&(this.u.dispose(),this.u=void 0),this.x?.()}get(){if(this.n,this.f.size===0){let t;try{this.s=!0;let r;this.w&&(r=this.w.createChangeSummary(void 0),this.w.beforeUpdate?.(this,r)),t=this._computeFn(this,r)}finally{this.s=!1}return this.h(),t}else{do{if(this.a===1){for(const t of this.j)if(t.reportChanges(),this.a===2)break}this.a===1&&(this.a=3),this.a!==3&&this.A()}while(this.a!==3);return this.c}}A(){let e=!1;this.n=!0,this.p=!1;const t=this.k;this.k=this.j,this.j=t;try{const r=this.l;this.s=!0,this.w&&(this.q=!0,this.w.beforeUpdate?.(this,r),this.q=!1,this.l=this.w?.createChangeSummary(r));const n=this.a!==0,i=this.c;this.a=3;const o=this.u;o!==void 0&&(this.u=void 0);try{this.t!==void 0&&(this.t.dispose(),this.t=void 0),this.c=this._computeFn(this,r)}finally{this.s=!1;for(const a of this.k)a.removeObserver(this);this.k.clear(),o!==void 0&&o.dispose()}e=this.p||n&&!this.y(i,this.c),F()?.handleObservableUpdated(this,{oldValue:i,newValue:this.c,change:void 0,didChange:e,hadValue:n})}catch(r){ut(r)}if(this.n=!1,!this.p&&e)for(const r of this.f)r.handleChange(this,void 0);else this.p=!1}toString(){return`LazyDerived<${this.debugName}>`}beginUpdate(e){if(this.m)throw new B("Cyclic deriveds are not supported yet!");this.i++,this.m=!0;try{const t=this.i===1;if(this.a===3&&(this.a=1,!t))for(const r of this.f)r.handlePossibleChange(this);if(t)for(const r of this.f)r.beginUpdate(this)}finally{this.m=!1}}endUpdate(e){if(this.i--,this.i===0){const t=[...this.f];for(const r of t)r.endUpdate(this);if(this.v){const r=[...this.v];this.v=null;for(const n of r)n.endUpdate(this)}}q1(()=>this.i>=0)}handlePossibleChange(e){if(this.a===3&&this.j.has(e)&&!this.k.has(e)){this.a=1;for(const t of this.f)t.handlePossibleChange(this)}}handleChange(e,t){if(this.j.has(e)&&!this.k.has(e)||this.q){F()?.handleDerivedDependencyChanged(this,e,t);let r=!1;try{r=this.w?this.w.handleChange({changedObservable:e,change:t,didChange:i=>i===e},this.l):!0}catch(i){ut(i)}const n=this.a===3;if(r&&(this.a===1||n)&&(this.a=2,n))for(const i of this.f)i.handlePossibleChange(this)}}B(){if(!this.s)throw new B("The reader object cannot be used outside its compute function!")}readObservable(e){this.B(),e.addObserver(this);const t=e.get();return this.j.add(e),this.k.delete(e),t}reportChange(e){this.B(),this.p=!0;for(const t of this.f)t.handleChange(this,e)}get store(){return this.B(),this.t===void 0&&(this.t=new Ee),this.t}get delayedStore(){return this.B(),this.u===void 0&&(this.u=new Ee),this.u}addObserver(e){const t=!this.f.has(e)&&this.i>0;super.addObserver(e),t&&(this.v&&this.v.has(e)?this.v.delete(e):e.beginUpdate(this))}removeObserver(e){this.f.has(e)&&this.i>0&&(this.v||(this.v=new Set),this.v.add(e)),super.removeObserver(e)}debugGetState(){return{state:this.a,updateCount:this.i,isComputing:this.n,dependencies:this.j,value:this.c}}debugSetValue(e){this.c=e}setValue(e,t,r){this.c=e;const n=this.f;t.updateObserver(this,this);for(const i of n)i.handleChange(this,r)}};function Is(e,t){return new he(new gt(e.owner,e.debugName,e.debugReferenceFn),t,void 0,e.onLastObserverRemoved,e.equalsFn??Jt)}Os(Is);function Lr(...e){let t,r,n;return e.length===3?[t,r,n]=e:[r,n]=e,new Ce(new gt(t,void 0,n),r,n,()=>Ce.globalTransaction,Jt)}var Ce=class extends e1{constructor(e,t,r,n,i){super(),this.e=e,this.i=t,this._getValue=r,this.j=n,this.k=i,this.c=!1,this.n=o=>{const a=this._getValue(o),l=this.a,u=!this.c||!this.k(l,a);let c=!1;u&&(this.a=a,this.c&&(c=!0,xs(this.j(),f=>{F()?.handleObservableUpdated(this,{oldValue:l,newValue:a,change:void 0,didChange:u,hadValue:this.c});for(const h of this.f)f.updateObserver(h,this),h.handleChange(this,void 0)},()=>{const f=this.l();return"Event fired"+(f?`: ${f}`:"")})),this.c=!0),c||F()?.handleObservableUpdated(this,{oldValue:l,newValue:a,change:void 0,didChange:u,hadValue:this.c})}}l(){return this.e.getDebugName(this)}get debugName(){const e=this.l();return"From Event"+(e?`: ${e}`:"")}g(){this.d=this.i(this.n)}h(){this.d.dispose(),this.d=void 0,this.c=!1,this.a=void 0}get(){return this.d?(this.c||this.n(void 0),this.a):this._getValue(void 0)}debugSetValue(e){this.a=e}};(function(e){e.Observer=Ce;function t(r,n){let i=!1;Ce.globalTransaction===void 0&&(Ce.globalTransaction=r,i=!0);try{n()}finally{i&&(Ce.globalTransaction=void 0)}}e.batchEventsGlobally=t})(Lr||(Lr={}));function Cr(e){const t=new Ar(!1,void 0);return e.addObserver(t),Re(()=>{e.removeObserver(t)})}Ts(Cr);function Ms(e,t){const r=new Ar(!0,t);e.addObserver(r);try{r.beginUpdate(e)}finally{r.endUpdate(e)}return Re(()=>{e.removeObserver(r)})}_s(Ms);var Ar=class{constructor(e,t){this.b=e,this.c=t,this.a=0}beginUpdate(e){this.a++}endUpdate(e){this.a===1&&this.b&&(this.c?this.c(e.get()):e.reportChanges()),this.a--}handlePossibleChange(e){}handleChange(e,t){}},bt;function Fs(e){bt||(bt=new kr,Qt(bt)),bt.addFilteredObj(e)}var kr=class{constructor(){this.a=0,this.f=new WeakMap}addFilteredObj(e){this.b||(this.b=new Set),this.b.add(e)}c(e){return this.b?.has(e)??!0}d(e){return js([Ie(qs("|  ",this.a)),e])}e(e){return e.hadValue?e.didChange?[Ie(" "),te(fe(e.oldValue,70),{color:"red",strikeThrough:!0}),Ie(" "),te(fe(e.newValue,60),{color:"green"})]:[Ie(" (unchanged)")]:[Ie(" "),te(fe(e.newValue,60),{color:"green"}),Ie(" (initial)")]}handleObservableCreated(e){if(e instanceof he){const t=e;if(this.f.set(t,new Set),!1){const n=[];t.__debugUpdating=n;const i=t.beginUpdate;t.beginUpdate=a=>(n.push(a),i.apply(t,[a]));const o=t.endUpdate;t.endUpdate=a=>{const l=n.indexOf(a);return l===-1&&console.error("endUpdate called without beginUpdate",t.debugName,a.debugName),n.splice(l,1),o.apply(t,[a])}}}}handleOnListenerCountChanged(e,t){}handleObservableUpdated(e,t){if(this.c(e)){if(e instanceof he){this._handleDerivedRecomputed(e,t);return}console.log(...this.d([Me("observable value changed"),te(e.debugName,{color:"BlueViolet"}),...this.e(t)]))}}formatChanges(e){if(e.size!==0)return te(" (changed deps: "+[...e].map(t=>t.debugName).join(", ")+")",{color:"gray"})}handleDerivedDependencyChanged(e,t,r){this.c(e)&&this.f.get(e)?.add(t)}_handleDerivedRecomputed(e,t){if(!this.c(e))return;const r=this.f.get(e);r&&(console.log(...this.d([Me("derived recomputed"),te(e.debugName,{color:"BlueViolet"}),...this.e(t),this.formatChanges(r),{data:[{fn:e._debugNameData.referenceFn??e._computeFn}]}])),r.clear())}handleDerivedCleared(e){this.c(e)&&console.log(...this.d([Me("derived cleared"),te(e.debugName,{color:"BlueViolet"})]))}handleFromEventObservableTriggered(e,t){this.c(e)&&console.log(...this.d([Me("observable from event triggered"),te(e.debugName,{color:"BlueViolet"}),...this.e(t),{data:[{fn:e._getValue}]}]))}handleAutorunCreated(e){this.c(e)&&this.f.set(e,new Set)}handleAutorunDisposed(e){}handleAutorunDependencyChanged(e,t,r){this.c(e)&&this.f.get(e).add(t)}handleAutorunStarted(e){const t=this.f.get(e);t&&(this.c(e)&&console.log(...this.d([Me("autorun"),te(e.debugName,{color:"BlueViolet"}),this.formatChanges(t),{data:[{fn:e._debugNameData.referenceFn??e._runFn}]}])),t.clear(),this.a++)}handleAutorunFinished(e){this.a--}handleBeginTransaction(e){let t=e.getDebugName();t===void 0&&(t=""),this.c(e)&&console.log(...this.d([Me("transaction"),te(t,{color:"BlueViolet"}),{data:[{fn:e._fn}]}])),this.a++}handleEndTransaction(){this.a--}};function js(e){const t=new Array,r=[];let n="";function i(a){if("length"in a)for(const l of a)l&&i(l);else"text"in a?(n+=`%c${a.text}`,t.push(a.style),a.data&&r.push(...a.data)):"data"in a&&r.push(...a.data)}i(e);const o=[n,...t];return o.push(...r),o}function Ie(e){return te(e,{color:"black"})}function Me(e){return te(Bs(`${e}: `,10),{color:"black",bold:!0})}function te(e,t={color:"black"}){function r(i){return Object.entries(i).reduce((o,[a,l])=>`${o}${a}:${l};`,"")}const n={color:t.color};return t.strikeThrough&&(n["text-decoration"]="line-through"),t.bold&&(n["font-weight"]="bold"),{text:e,style:r(n)}}function fe(e,t){switch(typeof e){case"number":return""+e;case"string":return e.length+2<=t?`"${e}"`:`"${e.substr(0,t-7)}"+...`;case"boolean":return e?"true":"false";case"undefined":return"undefined";case"object":return e===null?"null":Array.isArray(e)?Us(e,t):zs(e,t);case"symbol":return e.toString();case"function":return`[[Function${e.name?" "+e.name:""}]]`;default:return""+e}}function Us(e,t){let r="[ ",n=!0;for(const i of e){if(n||(r+=", "),r.length-5>t){r+="...";break}n=!1,r+=`${fe(i,t-r.length)}`}return r+=" ]",r}function zs(e,t){if(typeof e.toString=="function"&&e.toString!==Object.prototype.toString){const o=e.toString();return o.length<=t?o:o.substring(0,t-3)+"..."}const r=gr(e);let n=r?r+"(":"{ ",i=!0;for(const[o,a]of Object.entries(e)){if(i||(n+=", "),n.length-5>t){n+="...";break}i=!1,n+=`${o}: ${fe(a,t-n.length)}`}return n+=r?")":" }",n}function qs(e,t){let r="";for(let n=1;n<=t;n++)r+=e;return r}function Bs(e,t){for(;e.length<t;)e+=" ";return e}var Vs=class u1{static createHost(t,r){return new u1(t,r)}static createClient(t,r){return new u1(t,r)}constructor(t,r){this.b=t,this.c=r,this.a=this.b({handleNotification:o=>{const a=o,l=this.c().notifications[a[0]];if(!l)throw new Error(`Unknown notification "${a[0]}"!`);l(...a[1])},handleRequest:o=>{const a=o;try{return{type:"result",value:this.c().requests[a[0]](...a[1])}}catch(l){return{type:"error",value:l}}}});const n=new Proxy({},{get:(o,a)=>async(...l)=>{const u=await this.a.sendRequest([a,l]);if(u.type==="error")throw u.value;return u.value}}),i=new Proxy({},{get:(o,a)=>(...l)=>{this.a.sendNotification([a,l])}});this.api={notifications:i,requests:n}}};function Ws(e,t){const r=globalThis;let n=[],i;const{channel:o,handler:a}=Gs({sendNotification:u=>{i?i.sendNotification(u):n.push(u)}});let l;return(r.$$debugValueEditor_debugChannels??(r.$$debugValueEditor_debugChannels={}))[e]=u=>{l=t(),i=u;for(const c of n)u.sendNotification(c);return n=[],a},Vs.createClient(o,()=>{if(!l)throw new Error("Not supported");return l})}function Gs(e){let t;return{channel:n=>(t=n,{sendNotification:i=>{e.sendNotification(i)},sendRequest:i=>{throw new Error("not supported")}}),handler:{handleRequest:n=>n.type==="notification"?t?.handleNotification(n.data):t?.handleRequest(n.data)}}}function Sr(e,t){const r=e.split(`
`);for(let n=1;n<r.length;n++){const i=r[n];if(t&&t.test(i))continue;const o=i.match(/\$show(\d+)FramesUp/);if(o){const l=parseInt(o[1],10);n+=l-1;continue}const a=Hs(i);if(a)return a}}function Hs(e){const t=e.match(/\((.*):(\d+):(\d+)\)/);if(t)return{fileName:t[1],line:parseInt(t[2]),column:parseInt(t[3]),id:e};const r=e.match(/at ([^\(\)]*):(\d+):(\d+)/);if(r)return{fileName:r[1],line:parseInt(r[2]),column:parseInt(r[3]),id:e}}var Zs=class{constructor(){this.a=void 0}throttle(e,t){this.a===void 0&&(this.a=setTimeout(()=>{this.a=void 0,e()},t))}dispose(){this.a!==void 0&&clearTimeout(this.a)}};function $r(e,t){for(const r in t)e[r]&&typeof e[r]=="object"&&t[r]&&typeof t[r]=="object"?$r(e[r],t[r]):e[r]=t[r]}function xr(e,t){for(const r in t)t[r]===null?delete e[r]:e[r]&&typeof e[r]=="object"&&t[r]&&typeof t[r]=="object"?xr(e[r],t[r]):e[r]=t[r]}var Ys=class Ze{static{this.a=void 0}static getInstance(){return Ze.a===void 0&&(Ze.a=new Ze),Ze.a}j(){const t=[],r=[...this.h];if(r.length===0)return;const n=r.flatMap(o=>o.debugGetUpdatingObservers()??[]).map(o=>o.observer),i=new Set;for(;n.length>0;){const o=n.shift();if(i.has(o))continue;i.add(o);const a=this.n(o,l=>{i.has(l)||n.push(l)});a&&t.push(a)}return{names:r.map(o=>o.getDebugName()??"tx"),affected:t}}k(t){const r=this.f.get(t);if(!r){ue(new B("No info found"));return}return r}m(t){const r=this.f.get(t);if(!r){ue(new B("No info found"));return}return r}n(t,r){if(t instanceof he){const n=[...t.debugGetObservers()];for(const u of n)r(u);const i=this.k(t);if(!i)return;const o=t.debugGetState(),a={name:t.debugName,instanceId:i.instanceId,updateCount:o.updateCount},l=[...i.changedObservables].map(u=>this.f.get(u)?.instanceId).filter(qe);if(o.isComputing)return{...a,type:"observable/derived",state:"updating",changedDependencies:l,initialComputation:!1};switch(o.state){case 0:return{...a,type:"observable/derived",state:"noValue"};case 3:return{...a,type:"observable/derived",state:"upToDate"};case 2:return{...a,type:"observable/derived",state:"stale",changedDependencies:l};case 1:return{...a,type:"observable/derived",state:"possiblyStale"}}}else if(t instanceof Rs){const n=this.m(t);if(!n)return;const i={name:t.debugName,instanceId:n.instanceId,updateCount:n.updateCount},o=[...n.changedObservables].map(a=>this.f.get(a).instanceId);if(t.debugGetState().isRunning)return{...i,type:"autorun",state:"updating",changedDependencies:o};switch(t.debugGetState().state){case 3:return{...i,type:"autorun",state:"upToDate"};case 2:return{...i,type:"autorun",state:"stale",changedDependencies:o};case 1:return{...i,type:"autorun",state:"possiblyStale"}}}}p(t){const r=this.k(t);if(r)return{name:t.debugName,instanceId:r.instanceId}}q(t){if(t instanceof he)return{name:t.toString(),instanceId:this.k(t)?.instanceId};const r=this.m(t);if(r)return{name:t.toString(),instanceId:r.instanceId}}constructor(){this.b=0,this.c=0,this.e=new Map,this.f=new WeakMap,this.g=new Map,this.h=new Set,this.i=Ws("observableDevTools",()=>({notifications:{setDeclarationIdFilter:t=>{},logObservableValue:t=>{console.log("logObservableValue",t)},flushUpdates:()=>{this.w()},resetUpdates:()=>{this.r=null,this.i.api.notifications.handleChange(this.u,!0)}},requests:{getDeclarations:()=>{const t={};for(const r of this.e.values())t[r.id]=r;return{decls:t}},getSummarizedInstances:()=>null,getObservableValueInfo:t=>({observers:[...this.g.get(t).debugGetObservers()].map(n=>this.q(n)).filter(qe)}),getDerivedInfo:t=>{const r=this.g.get(t);return{dependencies:[...r.debugGetState().dependencies].map(n=>this.p(n)).filter(qe),observers:[...r.debugGetObservers()].map(n=>this.q(n)).filter(qe)}},getAutorunInfo:t=>({dependencies:[...this.g.get(t).debugGetState().dependencies].map(n=>this.p(n)).filter(qe)}),getTransactionState:()=>this.j(),setValue:(t,r)=>{const n=this.g.get(t);if(n instanceof he)n.debugSetValue(r);else if(n instanceof t1)n.debugSetValue(r);else if(n instanceof Ce)n.debugSetValue(r);else throw new B("Observable is not supported");const i=[...n.debugGetObservers()];for(const o of i)o.beginUpdate(n);for(const o of i)o.handleChange(n,void 0);for(const o of i)o.endUpdate(n)},getValue:t=>{const r=this.g.get(t);if(r instanceof he)return fe(r.debugGetState().value,200);if(r instanceof t1)return fe(r.debugGetState().value,200)}}})),this.r=null,this.s=new Zs,this.u={},this.w=()=>{this.r!==null&&(this.i.api.notifications.handleChange(this.r,!1),this.r=null)}}v(t){xr(this.u,t),this.r===null?this.r=t:$r(this.r,t),this.s.throttle(this.w,10)}x(t){let r=!0,n;const i=Error;for(;;){const a=i.stackTraceLimit;i.stackTraceLimit=r?6:20;const l=new Error().stack;i.stackTraceLimit=a;let u=Sr(l,/[/\\]observableInternal[/\\]|\.observe|[/\\]util(s)?\./);if(!r&&!u&&(u=Sr(l,/[/\\]observableInternal[/\\]|\.observe/)),u){n=u;break}if(!r){console.error("Could not find location for declaration",new Error().stack),n={fileName:"unknown",line:0,column:0,id:"unknown"};break}r=!1}let o=this.e.get(n.id);return o===void 0&&(o={id:this.b++,type:t,url:n.fileName,line:n.line,column:n.column},this.e.set(n.id,o),this.v({decls:{[o.id]:o}})),o.id}handleObservableCreated(t){const n={declarationId:this.x("observable/value"),instanceId:this.c++,listenerCount:0,lastValue:void 0,updateCount:0,changedObservables:new Set};this.f.set(t,n)}handleOnListenerCountChanged(t,r){const n=this.k(t);if(n){if(n.listenerCount===0&&r>0){const i=t instanceof he?"observable/derived":"observable/value";this.g.set(n.instanceId,t),this.v({instances:{[n.instanceId]:{instanceId:n.instanceId,declarationId:n.declarationId,formattedValue:n.lastValue,type:i,name:t.debugName}}})}else n.listenerCount>0&&r===0&&(this.v({instances:{[n.instanceId]:null}}),this.g.delete(n.instanceId));n.listenerCount=r}}handleObservableUpdated(t,r){if(t instanceof he){this._handleDerivedRecomputed(t,r);return}const n=this.k(t);n&&r.didChange&&(n.lastValue=fe(r.newValue,30),n.listenerCount>0&&this.v({instances:{[n.instanceId]:{formattedValue:n.lastValue}}}))}handleAutorunCreated(t){const n={declarationId:this.x("autorun"),instanceId:this.c++,updateCount:0,changedObservables:new Set};this.f.set(t,n),this.g.set(n.instanceId,t),n&&this.v({instances:{[n.instanceId]:{instanceId:n.instanceId,declarationId:n.declarationId,runCount:0,type:"autorun",name:t.debugName}}})}handleAutorunDisposed(t){const r=this.m(t);r&&(this.v({instances:{[r.instanceId]:null}}),this.f.delete(t),this.g.delete(r.instanceId))}handleAutorunDependencyChanged(t,r,n){const i=this.m(t);i&&i.changedObservables.add(r)}handleAutorunStarted(t){}handleAutorunFinished(t){const r=this.m(t);r&&(r.changedObservables.clear(),r.updateCount++,this.v({instances:{[r.instanceId]:{runCount:r.updateCount}}}))}handleDerivedDependencyChanged(t,r,n){const i=this.k(t);i&&i.changedObservables.add(r)}_handleDerivedRecomputed(t,r){const n=this.k(t);if(!n)return;const i=fe(r.newValue,30);n.updateCount++,n.changedObservables.clear(),n.lastValue=i,n.listenerCount>0&&this.v({instances:{[n.instanceId]:{formattedValue:i,recomputationCount:n.updateCount}}})}handleDerivedCleared(t){const r=this.k(t);r&&(r.lastValue=void 0,r.changedObservables.clear(),r.listenerCount>0&&this.v({instances:{[r.instanceId]:{formattedValue:void 0}}}))}handleBeginTransaction(t){this.h.add(t)}handleEndTransaction(t){this.h.delete(t)}};Ss(Fs);var Js=!1;Js&&Qt(new kr),Tt&&Tt.VSCODE_DEV_DEBUG&&Qt(Ys.getInstance());var G=class K{static fromTo(t,r){return new K(t,r)}static addRange(t,r){let n=0;for(;n<r.length&&r[n].endExclusive<t.start;)n++;let i=n;for(;i<r.length&&r[i].start<=t.endExclusive;)i++;if(n===i)r.splice(n,0,t);else{const o=Math.min(t.start,r[n].start),a=Math.max(t.endExclusive,r[i-1].endExclusive);r.splice(n,i-n,new K(o,a))}}static tryCreate(t,r){if(!(t>r))return new K(t,r)}static ofLength(t){return new K(0,t)}static ofStartAndLength(t,r){return new K(t,t+r)}static emptyAt(t){return new K(t,t)}constructor(t,r){if(this.start=t,this.endExclusive=r,t>r)throw new B(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(t){return new K(this.start+t,this.endExclusive+t)}deltaStart(t){return new K(this.start+t,this.endExclusive)}deltaEnd(t){return new K(this.start,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(t){return this.start===t.start&&this.endExclusive===t.endExclusive}containsRange(t){return this.start<=t.start&&t.endExclusive<=this.endExclusive}contains(t){return this.start<=t&&t<this.endExclusive}join(t){return new K(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}intersect(t){const r=Math.max(this.start,t.start),n=Math.min(this.endExclusive,t.endExclusive);if(r<=n)return new K(r,n)}intersectionLength(t){const r=Math.max(this.start,t.start),n=Math.min(this.endExclusive,t.endExclusive);return Math.max(0,n-r)}intersects(t){const r=Math.max(this.start,t.start),n=Math.min(this.endExclusive,t.endExclusive);return r<n}intersectsOrTouches(t){const r=Math.max(this.start,t.start),n=Math.min(this.endExclusive,t.endExclusive);return r<=n}isBefore(t){return this.endExclusive<=t.start}isAfter(t){return this.start>=t.endExclusive}slice(t){return t.slice(this.start,this.endExclusive)}substring(t){return t.substring(this.start,this.endExclusive)}clip(t){if(this.isEmpty)throw new B(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,t))}clipCyclic(t){if(this.isEmpty)throw new B(`Invalid clipping range: ${this.toString()}`);return t<this.start?this.endExclusive-(this.start-t)%this.length:t>=this.endExclusive?this.start+(t-this.start)%this.length:t}map(t){const r=[];for(let n=this.start;n<this.endExclusive;n++)r.push(t(n));return r}forEach(t){for(let r=this.start;r<this.endExclusive;r++)t(r)}joinRightTouching(t){if(this.endExclusive!==t.start)throw new B(`Invalid join: ${this.toString()} and ${t.toString()}`);return new K(this.start,t.endExclusive)}},mt=class $e{constructor(t,r){this.lineNumber=t,this.column=r}with(t=this.lineNumber,r=this.column){return t===this.lineNumber&&r===this.column?this:new $e(t,r)}delta(t=0,r=0){return this.with(Math.max(1,this.lineNumber+t),Math.max(1,this.column+r))}equals(t){return $e.equals(this,t)}static equals(t,r){return!t&&!r?!0:!!t&&!!r&&t.lineNumber===r.lineNumber&&t.column===r.column}isBefore(t){return $e.isBefore(this,t)}static isBefore(t,r){return t.lineNumber<r.lineNumber?!0:r.lineNumber<t.lineNumber?!1:t.column<r.column}isBeforeOrEqual(t){return $e.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,r){return t.lineNumber<r.lineNumber?!0:r.lineNumber<t.lineNumber?!1:t.column<=r.column}static compare(t,r){const n=t.lineNumber|0,i=r.lineNumber|0;if(n===i){const o=t.column|0,a=r.column|0;return o-a}return n-i}clone(){return new $e(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new $e(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},Or=class I{constructor(t,r,n,i){t>n||t===n&&r>i?(this.startLineNumber=n,this.startColumn=i,this.endLineNumber=t,this.endColumn=r):(this.startLineNumber=t,this.startColumn=r,this.endLineNumber=n,this.endColumn=i)}isEmpty(){return I.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return I.containsPosition(this,t)}static containsPosition(t,r){return!(r.lineNumber<t.startLineNumber||r.lineNumber>t.endLineNumber||r.lineNumber===t.startLineNumber&&r.column<t.startColumn||r.lineNumber===t.endLineNumber&&r.column>t.endColumn)}static strictContainsPosition(t,r){return!(r.lineNumber<t.startLineNumber||r.lineNumber>t.endLineNumber||r.lineNumber===t.startLineNumber&&r.column<=t.startColumn||r.lineNumber===t.endLineNumber&&r.column>=t.endColumn)}containsRange(t){return I.containsRange(this,t)}static containsRange(t,r){return!(r.startLineNumber<t.startLineNumber||r.endLineNumber<t.startLineNumber||r.startLineNumber>t.endLineNumber||r.endLineNumber>t.endLineNumber||r.startLineNumber===t.startLineNumber&&r.startColumn<t.startColumn||r.endLineNumber===t.endLineNumber&&r.endColumn>t.endColumn)}strictContainsRange(t){return I.strictContainsRange(this,t)}static strictContainsRange(t,r){return!(r.startLineNumber<t.startLineNumber||r.endLineNumber<t.startLineNumber||r.startLineNumber>t.endLineNumber||r.endLineNumber>t.endLineNumber||r.startLineNumber===t.startLineNumber&&r.startColumn<=t.startColumn||r.endLineNumber===t.endLineNumber&&r.endColumn>=t.endColumn)}plusRange(t){return I.plusRange(this,t)}static plusRange(t,r){let n,i,o,a;return r.startLineNumber<t.startLineNumber?(n=r.startLineNumber,i=r.startColumn):r.startLineNumber===t.startLineNumber?(n=r.startLineNumber,i=Math.min(r.startColumn,t.startColumn)):(n=t.startLineNumber,i=t.startColumn),r.endLineNumber>t.endLineNumber?(o=r.endLineNumber,a=r.endColumn):r.endLineNumber===t.endLineNumber?(o=r.endLineNumber,a=Math.max(r.endColumn,t.endColumn)):(o=t.endLineNumber,a=t.endColumn),new I(n,i,o,a)}intersectRanges(t){return I.intersectRanges(this,t)}static intersectRanges(t,r){let n=t.startLineNumber,i=t.startColumn,o=t.endLineNumber,a=t.endColumn;const l=r.startLineNumber,u=r.startColumn,c=r.endLineNumber,f=r.endColumn;return n<l?(n=l,i=u):n===l&&(i=Math.max(i,u)),o>c?(o=c,a=f):o===c&&(a=Math.min(a,f)),n>o||n===o&&i>a?null:new I(n,i,o,a)}equalsRange(t){return I.equalsRange(this,t)}static equalsRange(t,r){return!t&&!r?!0:!!t&&!!r&&t.startLineNumber===r.startLineNumber&&t.startColumn===r.startColumn&&t.endLineNumber===r.endLineNumber&&t.endColumn===r.endColumn}getEndPosition(){return I.getEndPosition(this)}static getEndPosition(t){return new mt(t.endLineNumber,t.endColumn)}getStartPosition(){return I.getStartPosition(this)}static getStartPosition(t){return new mt(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,r){return new I(this.startLineNumber,this.startColumn,t,r)}setStartPosition(t,r){return new I(t,r,this.endLineNumber,this.endColumn)}collapseToStart(){return I.collapseToStart(this)}static collapseToStart(t){return new I(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return I.collapseToEnd(this)}static collapseToEnd(t){return new I(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new I(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}isSingleLine(){return this.startLineNumber===this.endLineNumber}static fromPositions(t,r=t){return new I(t.lineNumber,t.column,r.lineNumber,r.column)}static lift(t){return t?new I(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,r){return!(t.endLineNumber<r.startLineNumber||t.endLineNumber===r.startLineNumber&&t.endColumn<r.startColumn||r.endLineNumber<t.startLineNumber||r.endLineNumber===t.startLineNumber&&r.endColumn<t.startColumn)}static areIntersecting(t,r){return!(t.endLineNumber<r.startLineNumber||t.endLineNumber===r.startLineNumber&&t.endColumn<=r.startColumn||r.endLineNumber<t.startLineNumber||r.endLineNumber===t.startLineNumber&&r.endColumn<=t.startColumn)}static areOnlyIntersecting(t,r){return!(t.endLineNumber<r.startLineNumber-1||t.endLineNumber===r.startLineNumber&&t.endColumn<r.startColumn-1||r.endLineNumber<t.startLineNumber-1||r.endLineNumber===t.startLineNumber&&r.endColumn<t.startColumn-1)}static compareRangesUsingStarts(t,r){if(t&&r){const o=t.startLineNumber|0,a=r.startLineNumber|0;if(o===a){const l=t.startColumn|0,u=r.startColumn|0;if(l===u){const c=t.endLineNumber|0,f=r.endLineNumber|0;if(c===f){const h=t.endColumn|0,b=r.endColumn|0;return h-b}return c-f}return l-u}return o-a}return(t?1:0)-(r?1:0)}static compareRangesUsingEnds(t,r){return t.endLineNumber===r.endLineNumber?t.endColumn===r.endColumn?t.startLineNumber===r.startLineNumber?t.startColumn-r.startColumn:t.startLineNumber-r.startLineNumber:t.endColumn-r.endColumn:t.endLineNumber-r.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}},Ae=class H{static ofLength(t,r){return new H(t,t+r)}static fromRange(t){return new H(t.startLineNumber,t.endLineNumber)}static fromRangeInclusive(t){return new H(t.startLineNumber,t.endLineNumber+1)}static{this.compareByStart=P1(t=>t.startLineNumber,R1)}static subtract(t,r){return r?t.startLineNumber<r.startLineNumber&&r.endLineNumberExclusive<t.endLineNumberExclusive?[new H(t.startLineNumber,r.startLineNumber),new H(r.endLineNumberExclusive,t.endLineNumberExclusive)]:r.startLineNumber<=t.startLineNumber&&t.endLineNumberExclusive<=r.endLineNumberExclusive?[]:r.endLineNumberExclusive<t.endLineNumberExclusive?[new H(Math.max(r.endLineNumberExclusive,t.startLineNumber),t.endLineNumberExclusive)]:[new H(t.startLineNumber,Math.min(r.startLineNumber,t.endLineNumberExclusive))]:[t]}static joinMany(t){if(t.length===0)return[];let r=new _r(t[0].slice());for(let n=1;n<t.length;n++)r=r.getUnion(new _r(t[n].slice()));return r.ranges}static join(t){if(t.length===0)throw new B("lineRanges cannot be empty");let r=t[0].startLineNumber,n=t[0].endLineNumberExclusive;for(let i=1;i<t.length;i++)r=Math.min(r,t[i].startLineNumber),n=Math.max(n,t[i].endLineNumberExclusive);return new H(r,n)}static deserialize(t){return new H(t[0],t[1])}constructor(t,r){if(t>r)throw new B(`startLineNumber ${t} cannot be after endLineNumberExclusive ${r}`);this.startLineNumber=t,this.endLineNumberExclusive=r}contains(t){return this.startLineNumber<=t&&t<this.endLineNumberExclusive}containsRange(t){return this.startLineNumber<=t.startLineNumber&&t.endLineNumberExclusive<=this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new H(this.startLineNumber+t,this.endLineNumberExclusive+t)}deltaLength(t){return new H(this.startLineNumber,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new H(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(t){const r=Math.max(this.startLineNumber,t.startLineNumber),n=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);if(r<=n)return new H(r,n)}intersectsStrict(t){return this.startLineNumber<t.endLineNumberExclusive&&t.startLineNumber<this.endLineNumberExclusive}intersectsOrTouches(t){return this.startLineNumber<=t.endLineNumberExclusive&&t.startLineNumber<=this.endLineNumberExclusive}equals(t){return this.startLineNumber===t.startLineNumber&&this.endLineNumberExclusive===t.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new Or(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new Or(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(t){const r=[];for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)r.push(t(n));return r}forEach(t){for(let r=this.startLineNumber;r<this.endLineNumberExclusive;r++)t(r)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}toOffsetRange(){return new G(this.startLineNumber-1,this.endLineNumberExclusive-1)}distanceToRange(t){return this.endLineNumberExclusive<=t.startLineNumber?t.startLineNumber-this.endLineNumberExclusive:t.endLineNumberExclusive<=this.startLineNumber?this.startLineNumber-t.endLineNumberExclusive:0}distanceToLine(t){return this.contains(t)?0:t<this.startLineNumber?this.startLineNumber-t:t-this.endLineNumberExclusive}addMargin(t,r){return new H(this.startLineNumber-t,this.endLineNumberExclusive+r)}},_r=class je{constructor(t=[]){this.c=t}get ranges(){return this.c}addRange(t){if(t.length===0)return;const r=_1(this.c,i=>i.endLineNumberExclusive>=t.startLineNumber),n=lt(this.c,i=>i.startLineNumber<=t.endLineNumberExclusive)+1;if(r===n)this.c.splice(r,0,t);else if(r===n-1){const i=this.c[r];this.c[r]=i.join(t)}else{const i=this.c[r].join(this.c[n-1]).join(t);this.c.splice(r,n-r,i)}}contains(t){const r=O1(this.c,n=>n.startLineNumber<=t);return!!r&&r.endLineNumberExclusive>t}intersects(t){const r=O1(this.c,n=>n.startLineNumber<t.endLineNumberExclusive);return!!r&&r.endLineNumberExclusive>t.startLineNumber}getUnion(t){if(this.c.length===0)return t;if(t.c.length===0)return this;const r=[];let n=0,i=0,o=null;for(;n<this.c.length||i<t.c.length;){let a=null;if(n<this.c.length&&i<t.c.length){const l=this.c[n],u=t.c[i];l.startLineNumber<u.startLineNumber?(a=l,n++):(a=u,i++)}else n<this.c.length?(a=this.c[n],n++):(a=t.c[i],i++);o===null?o=a:o.endLineNumberExclusive>=a.startLineNumber?o=new Ae(o.startLineNumber,Math.max(o.endLineNumberExclusive,a.endLineNumberExclusive)):(r.push(o),o=a)}return o!==null&&r.push(o),new je(r)}subtractFrom(t){const r=_1(this.c,a=>a.endLineNumberExclusive>=t.startLineNumber),n=lt(this.c,a=>a.startLineNumber<=t.endLineNumberExclusive)+1;if(r===n)return new je([t]);const i=[];let o=t.startLineNumber;for(let a=r;a<n;a++){const l=this.c[a];l.startLineNumber>o&&i.push(new Ae(o,l.startLineNumber)),o=l.endLineNumberExclusive}return o<t.endLineNumberExclusive&&i.push(new Ae(o,t.endLineNumberExclusive)),new je(i)}toString(){return this.c.map(t=>t.toString()).join(", ")}getIntersection(t){const r=[];let n=0,i=0;for(;n<this.c.length&&i<t.c.length;){const o=this.c[n],a=t.c[i],l=o.intersect(a);l&&!l.isEmpty&&r.push(l),o.endLineNumberExclusive<a.endLineNumberExclusive?n++:i++}return new je(r)}getWithDelta(t){return new je(this.c.map(r=>r.delta(t)))}},Tr;(function(e){e[e.MAX_SAFE_SMALL_INTEGER=1073741824]="MAX_SAFE_SMALL_INTEGER",e[e.MIN_SAFE_SMALL_INTEGER=-1073741824]="MIN_SAFE_SMALL_INTEGER",e[e.MAX_UINT_8=255]="MAX_UINT_8",e[e.MAX_UINT_16=65535]="MAX_UINT_16",e[e.MAX_UINT_32=4294967295]="MAX_UINT_32",e[e.UNICODE_SUPPLEMENTARY_PLANE_BEGIN=65536]="UNICODE_SUPPLEMENTARY_PLANE_BEGIN"})(Tr||(Tr={}));function Fe(e){return e<0?0:e>4294967295?4294967295:e|0}var Qs=class{constructor(e){this.a=e,this.b=new Uint32Array(e.length),this.c=new Int32Array(1),this.c[0]=-1}getCount(){return this.a.length}insertValues(e,t){e=Fe(e);const r=this.a,n=this.b,i=t.length;return i===0?!1:(this.a=new Uint32Array(r.length+i),this.a.set(r.subarray(0,e),0),this.a.set(r.subarray(e),e+i),this.a.set(t,e),e-1<this.c[0]&&(this.c[0]=e-1),this.b=new Uint32Array(this.a.length),this.c[0]>=0&&this.b.set(n.subarray(0,this.c[0]+1)),!0)}setValue(e,t){return e=Fe(e),t=Fe(t),this.a[e]===t?!1:(this.a[e]=t,e-1<this.c[0]&&(this.c[0]=e-1),!0)}removeValues(e,t){e=Fe(e),t=Fe(t);const r=this.a,n=this.b;if(e>=r.length)return!1;const i=r.length-e;return t>=i&&(t=i),t===0?!1:(this.a=new Uint32Array(r.length-t),this.a.set(r.subarray(0,e),0),this.a.set(r.subarray(e+t),e),this.b=new Uint32Array(this.a.length),e-1<this.c[0]&&(this.c[0]=e-1),this.c[0]>=0&&this.b.set(n.subarray(0,this.c[0]+1)),!0)}getTotalSum(){return this.a.length===0?0:this.d(this.a.length-1)}getPrefixSum(e){return e<0?0:(e=Fe(e),this.d(e))}d(e){if(e<=this.c[0])return this.b[e];let t=this.c[0]+1;t===0&&(this.b[0]=this.a[0],t++),e>=this.a.length&&(e=this.a.length-1);for(let r=t;r<=e;r++)this.b[r]=this.b[r-1]+this.a[r];return this.c[0]=Math.max(this.c[0],e),this.b[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,r=this.a.length-1,n=0,i=0,o=0;for(;t<=r;)if(n=t+(r-t)/2|0,i=this.b[n],o=i-this.a[n],e<o)r=n-1;else if(e>=i)t=n+1;else break;return new Xs(n,e-o)}},Xs=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},Ks=class{constructor(e,t,r,n){this.a=e,this.b=t,this.c=r,this.d=n,this.f=null,this.g=null}dispose(){this.b.length=0}get version(){return this.d}getText(){return this.g===null&&(this.g=this.b.join(this.c)),this.g}onEvents(e){e.eol&&e.eol!==this.c&&(this.c=e.eol,this.f=null);const t=e.changes;for(const r of t)this.k(r.range),this.l(new mt(r.range.startLineNumber,r.range.startColumn),r.text);this.d=e.versionId,this.g=null}h(){if(!this.f){const e=this.c.length,t=this.b.length,r=new Uint32Array(t);for(let n=0;n<t;n++)r[n]=this.b[n].length+e;this.f=new Qs(r)}}j(e,t){this.b[e]=t,this.f&&this.f.setValue(e,this.b[e].length+this.c.length)}k(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.startLineNumber-1].substring(e.endColumn-1));return}this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.endLineNumber-1].substring(e.endColumn-1)),this.b.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this.f&&this.f.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}l(e,t){if(t.length===0)return;const r=Zi(t);if(r.length===1){this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+r[0]+this.b[e.lineNumber-1].substring(e.column-1));return}r[r.length-1]+=this.b[e.lineNumber-1].substring(e.column-1),this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+r[0]);const n=new Uint32Array(r.length-1);for(let i=1;i<r.length;i++)this.b.splice(e.lineNumber+i-1,0,r[i]),n[i-1]=r[i].length+this.c.length;this.f&&this.f.insertValues(e.lineNumber,n)}},Dr;(function(e){e[e.Unknown=0]="Unknown",e[e.Invalid=3]="Invalid",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Dr||(Dr={}));function r1(e){let t=0,r=0,n=0,i=0;for(let o=0,a=e.length;o<a;o++){const l=e.charCodeAt(o);l===13?(t===0&&(r=o),t++,o+1<a&&e.charCodeAt(o+1)===10?(i|=2,o++):i|=3,n=o+1):l===10&&(i|=1,t===0&&(r=o),t++,n=o+1)}return t===0&&(r=e.length),[t,r,e.length-n,i]}var Pr=Object.create(null);function s(e,t){if(Si(t)){const r=Pr[t];if(r===void 0)throw new Error(`${e} references an unknown codicon: ${t}`);t=r}return Pr[e]=t,{id:e}}var eo={add:s("add",6e4),plus:s("plus",6e4),gistNew:s("gist-new",6e4),repoCreate:s("repo-create",6e4),lightbulb:s("lightbulb",60001),lightBulb:s("light-bulb",60001),repo:s("repo",60002),repoDelete:s("repo-delete",60002),gistFork:s("gist-fork",60003),repoForked:s("repo-forked",60003),gitPullRequest:s("git-pull-request",60004),gitPullRequestAbandoned:s("git-pull-request-abandoned",60004),recordKeys:s("record-keys",60005),keyboard:s("keyboard",60005),tag:s("tag",60006),gitPullRequestLabel:s("git-pull-request-label",60006),tagAdd:s("tag-add",60006),tagRemove:s("tag-remove",60006),person:s("person",60007),personFollow:s("person-follow",60007),personOutline:s("person-outline",60007),personFilled:s("person-filled",60007),gitBranch:s("git-branch",60008),gitBranchCreate:s("git-branch-create",60008),gitBranchDelete:s("git-branch-delete",60008),sourceControl:s("source-control",60008),mirror:s("mirror",60009),mirrorPublic:s("mirror-public",60009),star:s("star",60010),starAdd:s("star-add",60010),starDelete:s("star-delete",60010),starEmpty:s("star-empty",60010),comment:s("comment",60011),commentAdd:s("comment-add",60011),alert:s("alert",60012),warning:s("warning",60012),search:s("search",60013),searchSave:s("search-save",60013),logOut:s("log-out",60014),signOut:s("sign-out",60014),logIn:s("log-in",60015),signIn:s("sign-in",60015),eye:s("eye",60016),eyeUnwatch:s("eye-unwatch",60016),eyeWatch:s("eye-watch",60016),circleFilled:s("circle-filled",60017),primitiveDot:s("primitive-dot",60017),closeDirty:s("close-dirty",60017),debugBreakpoint:s("debug-breakpoint",60017),debugBreakpointDisabled:s("debug-breakpoint-disabled",60017),debugHint:s("debug-hint",60017),terminalDecorationSuccess:s("terminal-decoration-success",60017),primitiveSquare:s("primitive-square",60018),edit:s("edit",60019),pencil:s("pencil",60019),info:s("info",60020),issueOpened:s("issue-opened",60020),gistPrivate:s("gist-private",60021),gitForkPrivate:s("git-fork-private",60021),lock:s("lock",60021),mirrorPrivate:s("mirror-private",60021),close:s("close",60022),removeClose:s("remove-close",60022),x:s("x",60022),repoSync:s("repo-sync",60023),sync:s("sync",60023),clone:s("clone",60024),desktopDownload:s("desktop-download",60024),beaker:s("beaker",60025),microscope:s("microscope",60025),vm:s("vm",60026),deviceDesktop:s("device-desktop",60026),file:s("file",60027),fileText:s("file-text",60027),more:s("more",60028),ellipsis:s("ellipsis",60028),kebabHorizontal:s("kebab-horizontal",60028),mailReply:s("mail-reply",60029),reply:s("reply",60029),organization:s("organization",60030),organizationFilled:s("organization-filled",60030),organizationOutline:s("organization-outline",60030),newFile:s("new-file",60031),fileAdd:s("file-add",60031),newFolder:s("new-folder",60032),fileDirectoryCreate:s("file-directory-create",60032),trash:s("trash",60033),trashcan:s("trashcan",60033),history:s("history",60034),clock:s("clock",60034),folder:s("folder",60035),fileDirectory:s("file-directory",60035),symbolFolder:s("symbol-folder",60035),logoGithub:s("logo-github",60036),markGithub:s("mark-github",60036),github:s("github",60036),terminal:s("terminal",60037),console:s("console",60037),repl:s("repl",60037),zap:s("zap",60038),symbolEvent:s("symbol-event",60038),error:s("error",60039),stop:s("stop",60039),variable:s("variable",60040),symbolVariable:s("symbol-variable",60040),array:s("array",60042),symbolArray:s("symbol-array",60042),symbolModule:s("symbol-module",60043),symbolPackage:s("symbol-package",60043),symbolNamespace:s("symbol-namespace",60043),symbolObject:s("symbol-object",60043),symbolMethod:s("symbol-method",60044),symbolFunction:s("symbol-function",60044),symbolConstructor:s("symbol-constructor",60044),symbolBoolean:s("symbol-boolean",60047),symbolNull:s("symbol-null",60047),symbolNumeric:s("symbol-numeric",60048),symbolNumber:s("symbol-number",60048),symbolStructure:s("symbol-structure",60049),symbolStruct:s("symbol-struct",60049),symbolParameter:s("symbol-parameter",60050),symbolTypeParameter:s("symbol-type-parameter",60050),symbolKey:s("symbol-key",60051),symbolText:s("symbol-text",60051),symbolReference:s("symbol-reference",60052),goToFile:s("go-to-file",60052),symbolEnum:s("symbol-enum",60053),symbolValue:s("symbol-value",60053),symbolRuler:s("symbol-ruler",60054),symbolUnit:s("symbol-unit",60054),activateBreakpoints:s("activate-breakpoints",60055),archive:s("archive",60056),arrowBoth:s("arrow-both",60057),arrowDown:s("arrow-down",60058),arrowLeft:s("arrow-left",60059),arrowRight:s("arrow-right",60060),arrowSmallDown:s("arrow-small-down",60061),arrowSmallLeft:s("arrow-small-left",60062),arrowSmallRight:s("arrow-small-right",60063),arrowSmallUp:s("arrow-small-up",60064),arrowUp:s("arrow-up",60065),bell:s("bell",60066),bold:s("bold",60067),book:s("book",60068),bookmark:s("bookmark",60069),debugBreakpointConditionalUnverified:s("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:s("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:s("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:s("debug-breakpoint-data-unverified",60072),debugBreakpointData:s("debug-breakpoint-data",60073),debugBreakpointDataDisabled:s("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:s("debug-breakpoint-log-unverified",60074),debugBreakpointLog:s("debug-breakpoint-log",60075),debugBreakpointLogDisabled:s("debug-breakpoint-log-disabled",60075),briefcase:s("briefcase",60076),broadcast:s("broadcast",60077),browser:s("browser",60078),bug:s("bug",60079),calendar:s("calendar",60080),caseSensitive:s("case-sensitive",60081),check:s("check",60082),checklist:s("checklist",60083),chevronDown:s("chevron-down",60084),chevronLeft:s("chevron-left",60085),chevronRight:s("chevron-right",60086),chevronUp:s("chevron-up",60087),chromeClose:s("chrome-close",60088),chromeMaximize:s("chrome-maximize",60089),chromeMinimize:s("chrome-minimize",60090),chromeRestore:s("chrome-restore",60091),circleOutline:s("circle-outline",60092),circle:s("circle",60092),debugBreakpointUnverified:s("debug-breakpoint-unverified",60092),terminalDecorationIncomplete:s("terminal-decoration-incomplete",60092),circleSlash:s("circle-slash",60093),circuitBoard:s("circuit-board",60094),clearAll:s("clear-all",60095),clippy:s("clippy",60096),closeAll:s("close-all",60097),cloudDownload:s("cloud-download",60098),cloudUpload:s("cloud-upload",60099),code:s("code",60100),collapseAll:s("collapse-all",60101),colorMode:s("color-mode",60102),commentDiscussion:s("comment-discussion",60103),creditCard:s("credit-card",60105),dash:s("dash",60108),dashboard:s("dashboard",60109),database:s("database",60110),debugContinue:s("debug-continue",60111),debugDisconnect:s("debug-disconnect",60112),debugPause:s("debug-pause",60113),debugRestart:s("debug-restart",60114),debugStart:s("debug-start",60115),debugStepInto:s("debug-step-into",60116),debugStepOut:s("debug-step-out",60117),debugStepOver:s("debug-step-over",60118),debugStop:s("debug-stop",60119),debug:s("debug",60120),deviceCameraVideo:s("device-camera-video",60121),deviceCamera:s("device-camera",60122),deviceMobile:s("device-mobile",60123),diffAdded:s("diff-added",60124),diffIgnored:s("diff-ignored",60125),diffModified:s("diff-modified",60126),diffRemoved:s("diff-removed",60127),diffRenamed:s("diff-renamed",60128),diff:s("diff",60129),diffSidebyside:s("diff-sidebyside",60129),discard:s("discard",60130),editorLayout:s("editor-layout",60131),emptyWindow:s("empty-window",60132),exclude:s("exclude",60133),extensions:s("extensions",60134),eyeClosed:s("eye-closed",60135),fileBinary:s("file-binary",60136),fileCode:s("file-code",60137),fileMedia:s("file-media",60138),filePdf:s("file-pdf",60139),fileSubmodule:s("file-submodule",60140),fileSymlinkDirectory:s("file-symlink-directory",60141),fileSymlinkFile:s("file-symlink-file",60142),fileZip:s("file-zip",60143),files:s("files",60144),filter:s("filter",60145),flame:s("flame",60146),foldDown:s("fold-down",60147),foldUp:s("fold-up",60148),fold:s("fold",60149),folderActive:s("folder-active",60150),folderOpened:s("folder-opened",60151),gear:s("gear",60152),gift:s("gift",60153),gistSecret:s("gist-secret",60154),gist:s("gist",60155),gitCommit:s("git-commit",60156),gitCompare:s("git-compare",60157),compareChanges:s("compare-changes",60157),gitMerge:s("git-merge",60158),githubAction:s("github-action",60159),githubAlt:s("github-alt",60160),globe:s("globe",60161),grabber:s("grabber",60162),graph:s("graph",60163),gripper:s("gripper",60164),heart:s("heart",60165),home:s("home",60166),horizontalRule:s("horizontal-rule",60167),hubot:s("hubot",60168),inbox:s("inbox",60169),issueReopened:s("issue-reopened",60171),issues:s("issues",60172),italic:s("italic",60173),jersey:s("jersey",60174),json:s("json",60175),kebabVertical:s("kebab-vertical",60176),key:s("key",60177),law:s("law",60178),lightbulbAutofix:s("lightbulb-autofix",60179),linkExternal:s("link-external",60180),link:s("link",60181),listOrdered:s("list-ordered",60182),listUnordered:s("list-unordered",60183),liveShare:s("live-share",60184),loading:s("loading",60185),location:s("location",60186),mailRead:s("mail-read",60187),mail:s("mail",60188),markdown:s("markdown",60189),megaphone:s("megaphone",60190),mention:s("mention",60191),milestone:s("milestone",60192),gitPullRequestMilestone:s("git-pull-request-milestone",60192),mortarBoard:s("mortar-board",60193),move:s("move",60194),multipleWindows:s("multiple-windows",60195),mute:s("mute",60196),noNewline:s("no-newline",60197),note:s("note",60198),octoface:s("octoface",60199),openPreview:s("open-preview",60200),package:s("package",60201),paintcan:s("paintcan",60202),pin:s("pin",60203),play:s("play",60204),run:s("run",60204),plug:s("plug",60205),preserveCase:s("preserve-case",60206),preview:s("preview",60207),project:s("project",60208),pulse:s("pulse",60209),question:s("question",60210),quote:s("quote",60211),radioTower:s("radio-tower",60212),reactions:s("reactions",60213),references:s("references",60214),refresh:s("refresh",60215),regex:s("regex",60216),remoteExplorer:s("remote-explorer",60217),remote:s("remote",60218),remove:s("remove",60219),replaceAll:s("replace-all",60220),replace:s("replace",60221),repoClone:s("repo-clone",60222),repoForcePush:s("repo-force-push",60223),repoPull:s("repo-pull",60224),repoPush:s("repo-push",60225),report:s("report",60226),requestChanges:s("request-changes",60227),rocket:s("rocket",60228),rootFolderOpened:s("root-folder-opened",60229),rootFolder:s("root-folder",60230),rss:s("rss",60231),ruby:s("ruby",60232),saveAll:s("save-all",60233),saveAs:s("save-as",60234),save:s("save",60235),screenFull:s("screen-full",60236),screenNormal:s("screen-normal",60237),searchStop:s("search-stop",60238),server:s("server",60240),settingsGear:s("settings-gear",60241),settings:s("settings",60242),shield:s("shield",60243),smiley:s("smiley",60244),sortPrecedence:s("sort-precedence",60245),splitHorizontal:s("split-horizontal",60246),splitVertical:s("split-vertical",60247),squirrel:s("squirrel",60248),starFull:s("star-full",60249),starHalf:s("star-half",60250),symbolClass:s("symbol-class",60251),symbolColor:s("symbol-color",60252),symbolConstant:s("symbol-constant",60253),symbolEnumMember:s("symbol-enum-member",60254),symbolField:s("symbol-field",60255),symbolFile:s("symbol-file",60256),symbolInterface:s("symbol-interface",60257),symbolKeyword:s("symbol-keyword",60258),symbolMisc:s("symbol-misc",60259),symbolOperator:s("symbol-operator",60260),symbolProperty:s("symbol-property",60261),wrench:s("wrench",60261),wrenchSubaction:s("wrench-subaction",60261),symbolSnippet:s("symbol-snippet",60262),tasklist:s("tasklist",60263),telescope:s("telescope",60264),textSize:s("text-size",60265),threeBars:s("three-bars",60266),thumbsdown:s("thumbsdown",60267),thumbsup:s("thumbsup",60268),tools:s("tools",60269),triangleDown:s("triangle-down",60270),triangleLeft:s("triangle-left",60271),triangleRight:s("triangle-right",60272),triangleUp:s("triangle-up",60273),twitter:s("twitter",60274),unfold:s("unfold",60275),unlock:s("unlock",60276),unmute:s("unmute",60277),unverified:s("unverified",60278),verified:s("verified",60279),versions:s("versions",60280),vmActive:s("vm-active",60281),vmOutline:s("vm-outline",60282),vmRunning:s("vm-running",60283),watch:s("watch",60284),whitespace:s("whitespace",60285),wholeWord:s("whole-word",60286),window:s("window",60287),wordWrap:s("word-wrap",60288),zoomIn:s("zoom-in",60289),zoomOut:s("zoom-out",60290),listFilter:s("list-filter",60291),listFlat:s("list-flat",60292),listSelection:s("list-selection",60293),selection:s("selection",60293),listTree:s("list-tree",60294),debugBreakpointFunctionUnverified:s("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:s("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:s("debug-breakpoint-function-disabled",60296),debugStackframeActive:s("debug-stackframe-active",60297),circleSmallFilled:s("circle-small-filled",60298),debugStackframeDot:s("debug-stackframe-dot",60298),terminalDecorationMark:s("terminal-decoration-mark",60298),debugStackframe:s("debug-stackframe",60299),debugStackframeFocused:s("debug-stackframe-focused",60299),debugBreakpointUnsupported:s("debug-breakpoint-unsupported",60300),symbolString:s("symbol-string",60301),debugReverseContinue:s("debug-reverse-continue",60302),debugStepBack:s("debug-step-back",60303),debugRestartFrame:s("debug-restart-frame",60304),debugAlt:s("debug-alt",60305),callIncoming:s("call-incoming",60306),callOutgoing:s("call-outgoing",60307),menu:s("menu",60308),expandAll:s("expand-all",60309),feedback:s("feedback",60310),gitPullRequestReviewer:s("git-pull-request-reviewer",60310),groupByRefType:s("group-by-ref-type",60311),ungroupByRefType:s("ungroup-by-ref-type",60312),account:s("account",60313),gitPullRequestAssignee:s("git-pull-request-assignee",60313),bellDot:s("bell-dot",60314),debugConsole:s("debug-console",60315),library:s("library",60316),output:s("output",60317),runAll:s("run-all",60318),syncIgnored:s("sync-ignored",60319),pinned:s("pinned",60320),githubInverted:s("github-inverted",60321),serverProcess:s("server-process",60322),serverEnvironment:s("server-environment",60323),pass:s("pass",60324),issueClosed:s("issue-closed",60324),stopCircle:s("stop-circle",60325),playCircle:s("play-circle",60326),record:s("record",60327),debugAltSmall:s("debug-alt-small",60328),vmConnect:s("vm-connect",60329),cloud:s("cloud",60330),merge:s("merge",60331),export:s("export",60332),graphLeft:s("graph-left",60333),magnet:s("magnet",60334),notebook:s("notebook",60335),redo:s("redo",60336),checkAll:s("check-all",60337),pinnedDirty:s("pinned-dirty",60338),passFilled:s("pass-filled",60339),circleLargeFilled:s("circle-large-filled",60340),circleLarge:s("circle-large",60341),circleLargeOutline:s("circle-large-outline",60341),combine:s("combine",60342),gather:s("gather",60342),table:s("table",60343),variableGroup:s("variable-group",60344),typeHierarchy:s("type-hierarchy",60345),typeHierarchySub:s("type-hierarchy-sub",60346),typeHierarchySuper:s("type-hierarchy-super",60347),gitPullRequestCreate:s("git-pull-request-create",60348),runAbove:s("run-above",60349),runBelow:s("run-below",60350),notebookTemplate:s("notebook-template",60351),debugRerun:s("debug-rerun",60352),workspaceTrusted:s("workspace-trusted",60353),workspaceUntrusted:s("workspace-untrusted",60354),workspaceUnknown:s("workspace-unknown",60355),terminalCmd:s("terminal-cmd",60356),terminalDebian:s("terminal-debian",60357),terminalLinux:s("terminal-linux",60358),terminalPowershell:s("terminal-powershell",60359),terminalTmux:s("terminal-tmux",60360),terminalUbuntu:s("terminal-ubuntu",60361),terminalBash:s("terminal-bash",60362),arrowSwap:s("arrow-swap",60363),copy:s("copy",60364),personAdd:s("person-add",60365),filterFilled:s("filter-filled",60366),wand:s("wand",60367),debugLineByLine:s("debug-line-by-line",60368),inspect:s("inspect",60369),layers:s("layers",60370),layersDot:s("layers-dot",60371),layersActive:s("layers-active",60372),compass:s("compass",60373),compassDot:s("compass-dot",60374),compassActive:s("compass-active",60375),azure:s("azure",60376),issueDraft:s("issue-draft",60377),gitPullRequestClosed:s("git-pull-request-closed",60378),gitPullRequestDraft:s("git-pull-request-draft",60379),debugAll:s("debug-all",60380),debugCoverage:s("debug-coverage",60381),runErrors:s("run-errors",60382),folderLibrary:s("folder-library",60383),debugContinueSmall:s("debug-continue-small",60384),beakerStop:s("beaker-stop",60385),graphLine:s("graph-line",60386),graphScatter:s("graph-scatter",60387),pieChart:s("pie-chart",60388),bracket:s("bracket",60175),bracketDot:s("bracket-dot",60389),bracketError:s("bracket-error",60390),lockSmall:s("lock-small",60391),azureDevops:s("azure-devops",60392),verifiedFilled:s("verified-filled",60393),newline:s("newline",60394),layout:s("layout",60395),layoutActivitybarLeft:s("layout-activitybar-left",60396),layoutActivitybarRight:s("layout-activitybar-right",60397),layoutPanelLeft:s("layout-panel-left",60398),layoutPanelCenter:s("layout-panel-center",60399),layoutPanelJustify:s("layout-panel-justify",60400),layoutPanelRight:s("layout-panel-right",60401),layoutPanel:s("layout-panel",60402),layoutSidebarLeft:s("layout-sidebar-left",60403),layoutSidebarRight:s("layout-sidebar-right",60404),layoutStatusbar:s("layout-statusbar",60405),layoutMenubar:s("layout-menubar",60406),layoutCentered:s("layout-centered",60407),target:s("target",60408),indent:s("indent",60409),recordSmall:s("record-small",60410),errorSmall:s("error-small",60411),terminalDecorationError:s("terminal-decoration-error",60411),arrowCircleDown:s("arrow-circle-down",60412),arrowCircleLeft:s("arrow-circle-left",60413),arrowCircleRight:s("arrow-circle-right",60414),arrowCircleUp:s("arrow-circle-up",60415),layoutSidebarRightOff:s("layout-sidebar-right-off",60416),layoutPanelOff:s("layout-panel-off",60417),layoutSidebarLeftOff:s("layout-sidebar-left-off",60418),blank:s("blank",60419),heartFilled:s("heart-filled",60420),map:s("map",60421),mapHorizontal:s("map-horizontal",60421),foldHorizontal:s("fold-horizontal",60421),mapFilled:s("map-filled",60422),mapHorizontalFilled:s("map-horizontal-filled",60422),foldHorizontalFilled:s("fold-horizontal-filled",60422),circleSmall:s("circle-small",60423),bellSlash:s("bell-slash",60424),bellSlashDot:s("bell-slash-dot",60425),commentUnresolved:s("comment-unresolved",60426),gitPullRequestGoToChanges:s("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:s("git-pull-request-new-changes",60428),searchFuzzy:s("search-fuzzy",60429),commentDraft:s("comment-draft",60430),send:s("send",60431),sparkle:s("sparkle",60432),insert:s("insert",60433),mic:s("mic",60434),thumbsdownFilled:s("thumbsdown-filled",60435),thumbsupFilled:s("thumbsup-filled",60436),coffee:s("coffee",60437),snake:s("snake",60438),game:s("game",60439),vr:s("vr",60440),chip:s("chip",60441),piano:s("piano",60442),music:s("music",60443),micFilled:s("mic-filled",60444),repoFetch:s("repo-fetch",60445),copilot:s("copilot",60446),lightbulbSparkle:s("lightbulb-sparkle",60447),robot:s("robot",60448),sparkleFilled:s("sparkle-filled",60449),diffSingle:s("diff-single",60450),diffMultiple:s("diff-multiple",60451),surroundWith:s("surround-with",60452),share:s("share",60453),gitStash:s("git-stash",60454),gitStashApply:s("git-stash-apply",60455),gitStashPop:s("git-stash-pop",60456),vscode:s("vscode",60457),vscodeInsiders:s("vscode-insiders",60458),codeOss:s("code-oss",60459),runCoverage:s("run-coverage",60460),runAllCoverage:s("run-all-coverage",60461),coverage:s("coverage",60462),githubProject:s("github-project",60463),mapVertical:s("map-vertical",60464),foldVertical:s("fold-vertical",60464),mapVerticalFilled:s("map-vertical-filled",60465),foldVerticalFilled:s("fold-vertical-filled",60465),goToSearch:s("go-to-search",60466),percentage:s("percentage",60467),sortPercentage:s("sort-percentage",60467),attach:s("attach",60468),goToEditingSession:s("go-to-editing-session",60469),editSession:s("edit-session",60470),codeReview:s("code-review",60471),copilotWarning:s("copilot-warning",60472),python:s("python",60473),copilotLarge:s("copilot-large",60474),copilotWarningLarge:s("copilot-warning-large",60475),keyboardTab:s("keyboard-tab",60476),copilotBlocked:s("copilot-blocked",60477),copilotNotConnected:s("copilot-not-connected",60478),flag:s("flag",60479),lightbulbEmpty:s("lightbulb-empty",60480),symbolMethodArrow:s("symbol-method-arrow",60481),copilotUnavailable:s("copilot-unavailable",60482),repoPinned:s("repo-pinned",60483),keyboardTabAbove:s("keyboard-tab-above",60484),keyboardTabBelow:s("keyboard-tab-below",60485),gitPullRequestDone:s("git-pull-request-done",60486),mcp:s("mcp",60487),extensionsLarge:s("extensions-large",60488),layoutPanelDock:s("layout-panel-dock",60489),layoutSidebarLeftDock:s("layout-sidebar-left-dock",60490),layoutSidebarRightDock:s("layout-sidebar-right-dock",60491),copilotInProgress:s("copilot-in-progress",60492),copilotError:s("copilot-error",60493),copilotSuccess:s("copilot-success",60494),chatSparkle:s("chat-sparkle",60495),searchSparkle:s("search-sparkle",60496),editSparkle:s("edit-sparkle",60497),copilotSnooze:s("copilot-snooze",60498)},to={dialogError:s("dialog-error","error"),dialogWarning:s("dialog-warning","warning"),dialogInfo:s("dialog-info","info"),dialogClose:s("dialog-close","close"),treeItemExpanded:s("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:s("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:s("tree-filter-on-type-off","list-selection"),treeFilterClear:s("tree-filter-clear","close"),treeItemLoading:s("tree-item-loading","loading"),menuSelection:s("menu-selection","check"),menuSubmenu:s("menu-submenu","chevron-right"),menuBarMore:s("menubar-more","more"),scrollbarButtonLeft:s("scrollbar-button-left","triangle-left"),scrollbarButtonRight:s("scrollbar-button-right","triangle-right"),scrollbarButtonUp:s("scrollbar-button-up","triangle-up"),scrollbarButtonDown:s("scrollbar-button-down","triangle-down"),toolBarMore:s("toolbar-more","more"),quickInputBack:s("quick-input-back","arrow-left"),dropDownButton:s("drop-down-button",60084),symbolCustomColor:s("symbol-customcolor",60252),exportIcon:s("export",60332),workspaceUnspecified:s("workspace-unspecified",60355),newLine:s("newline",60394),thumbsDownFilled:s("thumbsdown-filled",60435),thumbsUpFilled:s("thumbsup-filled",60436),gitFetch:s("git-fetch",60445),lightbulbSparkleAutofix:s("lightbulb-sparkle-autofix",60447),debugBreakpointPending:s("debug-breakpoint-pending",60377)},v={...eo,...to},ro=class{constructor(){this.a=new Map,this.b=new Map,this.c=new ee,this.onDidChange=this.c.event,this.d=null}handleChange(e){this.c.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this.a.set(e,t),this.handleChange([e]),Re(()=>{this.a.get(e)===t&&(this.a.delete(e),this.handleChange([e]))})}get(e){return this.a.get(e)||null}registerFactory(e,t){this.b.get(e)?.dispose();const r=new no(this,e,t);return this.b.set(e,r),Re(()=>{const n=this.b.get(e);!n||n!==r||(this.b.delete(e),n.dispose())})}async getOrCreate(e){const t=this.get(e);if(t)return t;const r=this.b.get(e);return!r||r.isResolved?null:(await r.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;const r=this.b.get(e);return!!(!r||r.isResolved)}setColorMap(e){this.d=e,this.c.fire({changedLanguages:Array.from(this.a.keys()),changedColorMap:!0})}getColorMap(){return this.d}getDefaultBackground(){return this.d&&this.d.length>2?this.d[2]:null}},no=class extends ie{get isResolved(){return this.c}constructor(e,t,r){super(),this.f=e,this.g=t,this.h=r,this.a=!1,this.b=null,this.c=!1}dispose(){this.a=!0,super.dispose()}async resolve(){return this.b||(this.b=this.j()),this.b}async j(){const e=await this.h.tokenizationSupport;this.c=!0,e&&!this.a&&this.B(this.f.register(this.g,e))}},n1=class{constructor(e,t){this.tokens=e,this.endState=t,this._encodedTokenizationResultBrand=void 0}},Rr;(function(e){e[e.Increase=0]="Increase",e[e.Decrease=1]="Decrease"})(Rr||(Rr={}));var Ir;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Tool=27]="Tool",e[e.Snippet=28]="Snippet"})(Ir||(Ir={}));var Mr;(function(e){const t=new Map;t.set(0,v.symbolMethod),t.set(1,v.symbolFunction),t.set(2,v.symbolConstructor),t.set(3,v.symbolField),t.set(4,v.symbolVariable),t.set(5,v.symbolClass),t.set(6,v.symbolStruct),t.set(7,v.symbolInterface),t.set(8,v.symbolModule),t.set(9,v.symbolProperty),t.set(10,v.symbolEvent),t.set(11,v.symbolOperator),t.set(12,v.symbolUnit),t.set(13,v.symbolValue),t.set(15,v.symbolEnum),t.set(14,v.symbolConstant),t.set(15,v.symbolEnum),t.set(16,v.symbolEnumMember),t.set(17,v.symbolKeyword),t.set(28,v.symbolSnippet),t.set(18,v.symbolText),t.set(19,v.symbolColor),t.set(20,v.symbolFile),t.set(21,v.symbolReference),t.set(22,v.symbolCustomColor),t.set(23,v.symbolFolder),t.set(24,v.symbolTypeParameter),t.set(25,v.account),t.set(26,v.issues),t.set(27,v.tools);function r(a){let l=t.get(a);return l||(console.info("No codicon found for CompletionItemKind "+a),l=v.symbolProperty),l}e.toIcon=r;function n(a){switch(a){case 0:return y(824,null);case 1:return y(825,null);case 2:return y(826,null);case 3:return y(827,null);case 4:return y(828,null);case 5:return y(829,null);case 6:return y(830,null);case 7:return y(831,null);case 8:return y(832,null);case 9:return y(833,null);case 10:return y(834,null);case 11:return y(835,null);case 12:return y(836,null);case 13:return y(837,null);case 14:return y(838,null);case 15:return y(839,null);case 16:return y(840,null);case 17:return y(841,null);case 18:return y(842,null);case 19:return y(843,null);case 20:return y(844,null);case 21:return y(845,null);case 22:return y(846,null);case 23:return y(847,null);case 24:return y(848,null);case 25:return y(849,null);case 26:return y(850,null);case 27:return y(851,null);case 28:return y(852,null);default:return""}}e.toLabel=n;const i=new Map;i.set("method",0),i.set("function",1),i.set("constructor",2),i.set("field",3),i.set("variable",4),i.set("class",5),i.set("struct",6),i.set("interface",7),i.set("module",8),i.set("property",9),i.set("event",10),i.set("operator",11),i.set("unit",12),i.set("value",13),i.set("constant",14),i.set("enum",15),i.set("enum-member",16),i.set("enumMember",16),i.set("keyword",17),i.set("snippet",28),i.set("text",18),i.set("color",19),i.set("file",20),i.set("reference",21),i.set("customcolor",22),i.set("folder",23),i.set("type-parameter",24),i.set("typeParameter",24),i.set("account",25),i.set("issue",26),i.set("tool",27);function o(a,l){let u=i.get(a);return typeof u>"u"&&!l&&(u=9),u}e.fromString=o})(Mr||(Mr={}));var Fr;(function(e){e[e.Deprecated=1]="Deprecated"})(Fr||(Fr={}));var jr;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(jr||(jr={}));var Ur;(function(e){e[e.Word=0]="Word",e[e.Line=1]="Line",e[e.Suggest=2]="Suggest"})(Ur||(Ur={}));var zr;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(zr||(zr={}));var qr;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(qr||(qr={}));var Br;(function(e){e[e.Accepted=0]="Accepted",e[e.Rejected=1]="Rejected",e[e.Ignored=2]="Ignored"})(Br||(Br={}));var Vr;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(Vr||(Vr={}));var Wr;(function(e){e[e.Automatic=0]="Automatic",e[e.PasteAs=1]="PasteAs"})(Wr||(Wr={}));var Gr;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(Gr||(Gr={}));var Hr;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(Hr||(Hr={}));var Zr;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(Zr||(Zr={}));var L0={17:y(853,null),16:y(854,null),4:y(855,null),13:y(856,null),8:y(857,null),9:y(858,null),21:y(859,null),23:y(860,null),7:y(861,null),0:y(862,null),11:y(863,null),10:y(864,null),19:y(865,null),5:y(866,null),1:y(867,null),2:y(868,null),20:y(869,null),15:y(870,null),18:y(871,null),24:y(872,null),3:y(873,null),6:y(874,null),14:y(875,null),22:y(876,null),25:y(877,null),12:y(878,null)},Yr;(function(e){e[e.Deprecated=1]="Deprecated"})(Yr||(Yr={}));var Jr;(function(e){const t=new Map;t.set(0,v.symbolFile),t.set(1,v.symbolModule),t.set(2,v.symbolNamespace),t.set(3,v.symbolPackage),t.set(4,v.symbolClass),t.set(5,v.symbolMethod),t.set(6,v.symbolProperty),t.set(7,v.symbolField),t.set(8,v.symbolConstructor),t.set(9,v.symbolEnum),t.set(10,v.symbolInterface),t.set(11,v.symbolFunction),t.set(12,v.symbolVariable),t.set(13,v.symbolConstant),t.set(14,v.symbolString),t.set(15,v.symbolNumber),t.set(16,v.symbolBoolean),t.set(17,v.symbolArray),t.set(18,v.symbolObject),t.set(19,v.symbolKey),t.set(20,v.symbolNull),t.set(21,v.symbolEnumMember),t.set(22,v.symbolStruct),t.set(23,v.symbolEvent),t.set(24,v.symbolOperator),t.set(25,v.symbolTypeParameter);function r(o){let a=t.get(o);return a||(console.info("No codicon found for SymbolKind "+o),a=v.symbolProperty),a}e.toIcon=r;const n=new Map;n.set(0,20),n.set(1,8),n.set(2,8),n.set(3,8),n.set(4,5),n.set(5,0),n.set(6,9),n.set(7,3),n.set(8,2),n.set(9,15),n.set(10,7),n.set(11,1),n.set(12,4),n.set(13,14),n.set(14,18),n.set(15,13),n.set(16,13),n.set(17,13),n.set(18,13),n.set(19,17),n.set(20,13),n.set(21,16),n.set(22,6),n.set(23,10),n.set(24,11),n.set(25,24);function i(o){let a=n.get(o);return a===void 0&&(console.info("No completion kind found for SymbolKind "+o),a=20),a}e.toCompletionKind=i})(Jr||(Jr={}));var C0=class pe{static{this.Comment=new pe("comment")}static{this.Imports=new pe("imports")}static{this.Region=new pe("region")}static fromValue(t){switch(t){case"comment":return pe.Comment;case"imports":return pe.Imports;case"region":return pe.Region}return new pe(t)}constructor(t){this.value=t}},Qr;(function(e){e[e.AIGenerated=1]="AIGenerated"})(Qr||(Qr={}));var Xr;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(Xr||(Xr={}));var Kr;(function(e){function t(r){return!r||typeof r!="object"?!1:typeof r.id=="string"&&typeof r.title=="string"}e.is=t})(Kr||(Kr={}));var en;(function(e){e[e.Collapsed=0]="Collapsed",e[e.Expanded=1]="Expanded"})(en||(en={}));var tn;(function(e){e[e.Unresolved=0]="Unresolved",e[e.Resolved=1]="Resolved"})(tn||(tn={}));var rn;(function(e){e[e.Current=0]="Current",e[e.Outdated=1]="Outdated"})(rn||(rn={}));var nn;(function(e){e[e.Editing=0]="Editing",e[e.Preview=1]="Preview"})(nn||(nn={}));var sn;(function(e){e[e.Published=0]="Published",e[e.Draft=1]="Draft"})(sn||(sn={}));var on;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(on||(on={}));var A0=new ro,an;(function(e){e[e.None=0]="None",e[e.Option=1]="Option",e[e.Default=2]="Default",e[e.Preferred=3]="Preferred"})(an||(an={}));var io=new class{clone(){return this}equals(e){return this===e}};function so(e,t){const r=new Uint32Array(2);return r[0]=0,r[1]=(e<<0|0|0|32768|2<<24)>>>0,new n1(r,t===null?io:t)}var oo=class{constructor(e){this.b=e,this.a=[]}get(e){return e<this.a.length?this.a[e]:this.b}set(e,t){for(;e>=this.a.length;)this.a[this.a.length]=this.b;this.a[e]=t}replace(e,t,r){if(e>=this.a.length)return;if(t===0){this.insert(e,r);return}else if(r===0){this.delete(e,t);return}const n=this.a.slice(0,e),i=this.a.slice(e+t),o=ao(r,this.b);this.a=n.concat(o,i)}delete(e,t){t===0||e>=this.a.length||this.a.splice(e,t)}insert(e,t){if(t===0||e>=this.a.length)return;const r=[];for(let n=0;n<t;n++)r[n]=this.b;this.a=D1(this.a,e,r)}};function ao(e,t){const r=[];for(let n=0;n<e;n++)r[n]=t;return r}var k0=new qt(()=>new Uint8Array(256));function pt(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function vt(e,t,r){e[r+3]=t,t=t>>>8,e[r+2]=t,t=t>>>8,e[r+1]=t,t=t>>>8,e[r]=t}var ln;(function(e){e[e.Null=0]="Null",e[e.PlainText=1]="PlainText"})(ln||(ln={}));var un;(function(e){e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough"})(un||(un={}));var cn;(function(e){e[e.None=0]="None",e[e.DefaultForeground=1]="DefaultForeground",e[e.DefaultBackground=2]="DefaultBackground"})(cn||(cn={}));var hn;(function(e){e[e.Other=0]="Other",e[e.Comment=1]="Comment",e[e.String=2]="String",e[e.RegEx=3]="RegEx"})(hn||(hn={}));var fn;(function(e){e[e.LANGUAGEID_MASK=255]="LANGUAGEID_MASK",e[e.TOKEN_TYPE_MASK=768]="TOKEN_TYPE_MASK",e[e.BALANCED_BRACKETS_MASK=1024]="BALANCED_BRACKETS_MASK",e[e.FONT_STYLE_MASK=30720]="FONT_STYLE_MASK",e[e.FOREGROUND_MASK=16744448]="FOREGROUND_MASK",e[e.BACKGROUND_MASK=4278190080]="BACKGROUND_MASK",e[e.ITALIC_MASK=2048]="ITALIC_MASK",e[e.BOLD_MASK=4096]="BOLD_MASK",e[e.UNDERLINE_MASK=8192]="UNDERLINE_MASK",e[e.STRIKETHROUGH_MASK=16384]="STRIKETHROUGH_MASK",e[e.SEMANTIC_USE_ITALIC=1]="SEMANTIC_USE_ITALIC",e[e.SEMANTIC_USE_BOLD=2]="SEMANTIC_USE_BOLD",e[e.SEMANTIC_USE_UNDERLINE=4]="SEMANTIC_USE_UNDERLINE",e[e.SEMANTIC_USE_STRIKETHROUGH=8]="SEMANTIC_USE_STRIKETHROUGH",e[e.SEMANTIC_USE_FOREGROUND=16]="SEMANTIC_USE_FOREGROUND",e[e.SEMANTIC_USE_BACKGROUND=32]="SEMANTIC_USE_BACKGROUND",e[e.LANGUAGEID_OFFSET=0]="LANGUAGEID_OFFSET",e[e.TOKEN_TYPE_OFFSET=8]="TOKEN_TYPE_OFFSET",e[e.BALANCED_BRACKETS_OFFSET=10]="BALANCED_BRACKETS_OFFSET",e[e.FONT_STYLE_OFFSET=11]="FONT_STYLE_OFFSET",e[e.FOREGROUND_OFFSET=15]="FOREGROUND_OFFSET",e[e.BACKGROUND_OFFSET=24]="BACKGROUND_OFFSET"})(fn||(fn={}));var ke=class{static getLanguageId(e){return(e&255)>>>0}static getTokenType(e){return(e&768)>>>8}static containsBalancedBrackets(e){return(e&1024)!==0}static getFontStyle(e){return(e&30720)>>>11}static getForeground(e){return(e&16744448)>>>15}static getBackground(e){return(e&4278190080)>>>24}static getClassNameFromMetadata(e){let r="mtk"+this.getForeground(e);const n=this.getFontStyle(e);return n&1&&(r+=" mtki"),n&2&&(r+=" mtkb"),n&4&&(r+=" mtku"),n&8&&(r+=" mtks"),r}static getInlineStyleFromMetadata(e,t){const r=this.getForeground(e),n=this.getFontStyle(e);let i=`color: ${t[r]};`;n&1&&(i+="font-style: italic;"),n&2&&(i+="font-weight: bold;");let o="";return n&4&&(o+=" underline"),n&8&&(o+=" line-through"),o&&(i+=`text-decoration:${o};`),i}static getPresentationFromMetadata(e){const t=this.getForeground(e),r=this.getFontStyle(e);return{foreground:t,italic:!!(r&1),bold:!!(r&2),underline:!!(r&4),strikethrough:!!(r&8)}}},wt=class xe{static createEmpty(t,r){const n=xe.defaultTokenMetadata,i=new Uint32Array(2);return i[0]=t.length,i[1]=n,new xe(i,t,r)}static createFromTextAndMetadata(t,r){let n=0,i="";const o=new Array;for(const{text:a,metadata:l}of t)o.push(n+a.length,l),n+=a.length,i+=a;return new xe(new Uint32Array(o),i,r)}static convertToEndOffset(t,r){const i=(t.length>>>1)-1;for(let o=0;o<i;o++)t[o<<1]=t[o+1<<1];t[i<<1]=r}static findIndexInTokensArray(t,r){if(t.length<=2)return 0;let n=0,i=(t.length>>>1)-1;for(;n<i;){const o=n+Math.floor((i-n)/2),a=t[o<<1];if(a===r)return o+1;a<r?n=o+1:a>r&&(i=o)}return n}static{this.defaultTokenMetadata=(32768|2<<24)>>>0}constructor(t,r,n){this._lineTokensBrand=void 0,(t.length>1?t[t.length-2]:0)!==r.length&&ue(new Error("Token length and text length do not match!")),this.a=t,this.b=this.a.length>>>1,this.c=r,this.languageIdCodec=n}getTextLength(){return this.c.length}equals(t){return t instanceof xe?this.slicedEquals(t,0,this.b):!1}slicedEquals(t,r,n){if(this.c!==t.c||this.b!==t.b)return!1;const i=r<<1,o=i+(n<<1);for(let a=i;a<o;a++)if(this.a[a]!==t.a[a])return!1;return!0}getLineContent(){return this.c}getCount(){return this.b}getStartOffset(t){return t>0?this.a[t-1<<1]:0}getMetadata(t){return this.a[(t<<1)+1]}getLanguageId(t){const r=this.a[(t<<1)+1],n=ke.getLanguageId(r);return this.languageIdCodec.decodeLanguageId(n)}getStandardTokenType(t){const r=this.a[(t<<1)+1];return ke.getTokenType(r)}getForeground(t){const r=this.a[(t<<1)+1];return ke.getForeground(r)}getClassName(t){const r=this.a[(t<<1)+1];return ke.getClassNameFromMetadata(r)}getInlineStyle(t,r){const n=this.a[(t<<1)+1];return ke.getInlineStyleFromMetadata(n,r)}getPresentation(t){const r=this.a[(t<<1)+1];return ke.getPresentationFromMetadata(r)}getEndOffset(t){return this.a[t<<1]}findTokenIndexAtOffset(t){return xe.findIndexInTokensArray(this.a,t)}inflate(){return this}sliceAndInflate(t,r,n){return new lo(this,t,r,n)}sliceZeroCopy(t){return this.sliceAndInflate(t.start,t.endExclusive,0)}withInserted(t){if(t.length===0)return this;let r=0,n=0,i="";const o=new Array;let a=0;for(;;){const l=r<this.b?this.a[r<<1]:-1,u=n<t.length?t[n]:null;if(l!==-1&&(u===null||l<=u.offset)){i+=this.c.substring(a,l);const c=this.a[(r<<1)+1];o.push(i.length,c),r++,a=l}else if(u){if(u.offset>a){i+=this.c.substring(a,u.offset);const c=this.a[(r<<1)+1];o.push(i.length,c),a=u.offset}i+=u.text,o.push(i.length,u.tokenMetadata),n++}else break}return new xe(new Uint32Array(o),i,this.languageIdCodec)}getTokensInRange(t){const r=new co,n=this.findTokenIndexAtOffset(t.start),i=this.findTokenIndexAtOffset(t.endExclusive);for(let o=n;o<=i;o++){const l=new G(this.getStartOffset(o),this.getEndOffset(o)).intersectionLength(t);l>0&&r.add(l,this.getMetadata(o))}return r.build()}getTokenText(t){const r=this.getStartOffset(t),n=this.getEndOffset(t);return this.c.substring(r,n)}forEach(t){const r=this.getCount();for(let n=0;n<r;n++)t(n)}toString(){let t="";return this.forEach(r=>{t+=`[${this.getTokenText(r)}]{${this.getClassName(r)}}`}),t}},lo=class Sn{constructor(t,r,n,i){this.a=t,this.b=r,this.c=n,this.d=i,this.e=t.findTokenIndexAtOffset(r),this.languageIdCodec=t.languageIdCodec,this.f=0;for(let o=this.e,a=t.getCount();o<a&&!(t.getStartOffset(o)>=n);o++)this.f++}getMetadata(t){return this.a.getMetadata(this.e+t)}getLanguageId(t){return this.a.getLanguageId(this.e+t)}getLineContent(){return this.a.getLineContent().substring(this.b,this.c)}equals(t){return t instanceof Sn?this.b===t.b&&this.c===t.c&&this.d===t.d&&this.a.slicedEquals(t.a,this.e,this.f):!1}getCount(){return this.f}getStandardTokenType(t){return this.a.getStandardTokenType(this.e+t)}getForeground(t){return this.a.getForeground(this.e+t)}getEndOffset(t){const r=this.a.getEndOffset(this.e+t);return Math.min(this.c,r)-this.b+this.d}getClassName(t){return this.a.getClassName(this.e+t)}getInlineStyle(t,r){return this.a.getInlineStyle(this.e+t,r)}getPresentation(t){return this.a.getPresentation(this.e+t)}findTokenIndexAtOffset(t){return this.a.findTokenIndexAtOffset(t+this.b-this.d)-this.e}getTokenText(t){const r=this.e+t,n=this.a.getStartOffset(r),i=this.a.getEndOffset(r);let o=this.a.getTokenText(r);return n<this.b&&(o=o.substring(this.b-n)),i>this.c&&(o=o.substring(0,o.length-(i-this.c))),o}forEach(t){for(let r=0;r<this.getCount();r++)t(r)}},uo=class Ye{static fromLineTokens(t){const r=[];for(let n=0;n<t.getCount();n++)r.push(new i1(t.getEndOffset(n)-t.getStartOffset(n),t.getMetadata(n)));return Ye.create(r)}static create(t){return new Ye(t)}constructor(t){this.a=t}toLineTokens(t,r){return wt.createFromTextAndMetadata(this.map((n,i)=>({text:n.substring(t),metadata:i.metadata})),r)}forEach(t){let r=0;for(const n of this.a){const i=new G(r,r+n.length);t(i,n),r+=n.length}}map(t){const r=[];let n=0;for(const i of this.a){const o=new G(n,n+i.length);r.push(t(o,i)),n+=i.length}return r}slice(t){const r=[];let n=0;for(const i of this.a){const o=n,a=o+i.length;if(a>t.start){if(o>=t.endExclusive)break;const l=Math.max(0,t.start-o),u=Math.max(0,a-t.endExclusive);r.push(new i1(i.length-l-u,i.metadata))}n+=i.length}return Ye.create(r)}append(t){const r=this.a.concat(t.a);return Ye.create(r)}},i1=class{constructor(e,t){this.length=e,this.metadata=t}},co=class{constructor(){this.a=[]}add(e,t){this.a.push(new i1(e,t))}build(){return uo.create(this.a)}},Se=new Uint32Array(0).buffer,de=class c1{static deleteBeginning(t,r){return t===null||t===Se?t:c1.delete(t,0,r)}static deleteEnding(t,r){if(t===null||t===Se)return t;const n=Be(t),i=n[n.length-2];return c1.delete(t,r,i)}static delete(t,r,n){if(t===null||t===Se||r===n)return t;const i=Be(t),o=i.length>>>1;if(r===0&&i[i.length-2]===n)return Se;const a=wt.findIndexInTokensArray(i,r),l=a>0?i[a-1<<1]:0,u=i[a<<1];if(n<u){const w=n-r;for(let k=a;k<o;k++)i[k<<1]-=w;return t}let c,f;l!==r?(i[a<<1]=r,c=a+1<<1,f=r):(c=a<<1,f=l);const h=n-r;for(let w=a+1;w<o;w++){const k=i[w<<1]-h;k>f&&(i[c++]=k,i[c++]=i[(w<<1)+1],f=k)}if(c===i.length)return t;const b=new Uint32Array(c);return b.set(i.subarray(0,c),0),b.buffer}static append(t,r){if(r===Se)return t;if(t===Se)return r;if(t===null)return t;if(r===null)return null;const n=Be(t),i=Be(r),o=i.length>>>1,a=new Uint32Array(n.length+i.length);a.set(n,0);let l=n.length;const u=n[n.length-2];for(let c=0;c<o;c++)a[l++]=i[c<<1]+u,a[l++]=i[(c<<1)+1];return a.buffer}static insert(t,r,n){if(t===null||t===Se)return t;const i=Be(t),o=i.length>>>1;let a=wt.findIndexInTokensArray(i,r);a>0&&i[a-1<<1]===r&&a--;for(let l=a;l<o;l++)i[l<<1]+=n;return t}};function Be(e){return e instanceof Uint32Array?e:new Uint32Array(e)}var dn=class $n{static deserialize(t,r,n){const i=new Uint32Array(t.buffer),o=pt(t,r);r+=4;const a=pt(t,r);r+=4;const l=[];for(let u=0;u<a;u++){const c=pt(t,r);r+=4,l.push(i.subarray(r/4,r/4+c/4)),r+=c}return n.push(new $n(o,l)),r}get startLineNumber(){return this.a}get endLineNumber(){return this.a+this.b.length-1}constructor(t,r){this.a=t,this.b=r}getLineRange(){return new Ae(this.a,this.a+this.b.length)}getLineTokens(t){return this.b[t-this.a]}appendLineTokens(t){this.b.push(t)}serializeSize(){let t=0;t+=4,t+=4;for(let r=0;r<this.b.length;r++){const n=this.b[r];if(!(n instanceof Uint32Array))throw new Error("Not supported!");t+=4,t+=n.byteLength}return t}serialize(t,r){vt(t,this.a,r),r+=4,vt(t,this.b.length,r),r+=4;for(let n=0;n<this.b.length;n++){const i=this.b[n];if(!(i instanceof Uint32Array))throw new Error("Not supported!");vt(t,i.byteLength,r),r+=4,t.set(new Uint8Array(i.buffer),r),r+=i.byteLength}return r}applyEdit(t,r){const[n,i]=r1(r);this.c(t),this.d(new mt(t.startLineNumber,t.startColumn),n,i)}c(t){if(t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn)return;const r=t.startLineNumber-this.a,n=t.endLineNumber-this.a;if(n<0){const i=n-r;this.a-=i;return}if(!(r>=this.b.length)){if(r<0&&n>=this.b.length){this.a=0,this.b=[];return}if(r===n){this.b[r]=de.delete(this.b[r],t.startColumn-1,t.endColumn-1);return}if(r>=0)if(this.b[r]=de.deleteEnding(this.b[r],t.startColumn-1),n<this.b.length){const i=de.deleteBeginning(this.b[n],t.endColumn-1);this.b[r]=de.append(this.b[r],i),this.b.splice(r+1,n-r)}else this.b[r]=de.append(this.b[r],null),this.b=this.b.slice(0,r+1);else{const i=-r;this.a-=i,this.b[n]=de.deleteBeginning(this.b[n],t.endColumn-1),this.b=this.b.slice(n)}}}d(t,r,n){if(r===0&&n===0)return;const i=t.lineNumber-this.a;if(i<0){this.a+=r;return}if(!(i>=this.b.length)){if(r===0){this.b[i]=de.insert(this.b[i],t.column-1,n);return}this.b[i]=de.deleteEnding(this.b[i],t.column-1),this.b[i]=de.insert(this.b[i],t.column-1,n),this.e(t.lineNumber,r)}}e(t,r){if(r===0)return;const n=[];for(let i=0;i<r;i++)n[i]=null;this.b=D1(this.b,t,n)}},ho=class{static deserialize(e){let t=0;const r=pt(e,t);t+=4;const n=[];for(let i=0;i<r;i++)t=dn.deserialize(e,t,n);return n}constructor(){this.a=[]}add(e,t){if(this.a.length>0){const r=this.a[this.a.length-1];if(r.endLineNumber+1===e){r.appendLineTokens(t);return}}this.a.push(new dn(e,[t]))}finalize(){return this.a}serialize(){const e=this.b(),t=new Uint8Array(e);return this.c(t),t}b(){let e=0;e+=4;for(let t=0;t<this.a.length;t++)e+=this.a[t].serializeSize();return e}c(e){let t=0;vt(e,this.a.length,t),t+=4;for(let r=0;r<this.a.length;r++)t=this.a[r].serialize(e,t)}},gn;(function(e){e[e.CHEAP_TOKENIZATION_LENGTH_LIMIT=2048]="CHEAP_TOKENIZATION_LENGTH_LIMIT"})(gn||(gn={}));var fo=class{constructor(e,t){this.tokenizationSupport=t,this.a=this.tokenizationSupport.getInitialState(),this.store=new go(e)}getStartState(e){return this.store.getStartState(e,this.a)}getFirstInvalidLine(){return this.store.getFirstInvalidLine(this.a)}},go=class{constructor(e){this.d=e,this.a=new bo,this.b=new mo,this.b.addRange(new G(1,e+1))}getEndState(e){return this.a.getEndState(e)}setEndState(e,t){if(!t)throw new B("Cannot set null/undefined state");this.b.delete(e);const r=this.a.setEndState(e,t);return r&&e<this.d&&this.b.addRange(new G(e+1,e+2)),r}acceptChange(e,t){this.d+=t-e.length,this.a.acceptChange(e,t),this.b.addRangeAndResize(new G(e.startLineNumber,e.endLineNumberExclusive),t)}acceptChanges(e){for(const t of e){const[r]=r1(t.text);this.acceptChange(new Ae(t.range.startLineNumber,t.range.endLineNumber+1),r+1)}}invalidateEndStateRange(e){this.b.addRange(new G(e.startLineNumber,e.endLineNumberExclusive))}getFirstInvalidEndStateLineNumber(){return this.b.min}getFirstInvalidEndStateLineNumberOrMax(){return this.getFirstInvalidEndStateLineNumber()||Number.MAX_SAFE_INTEGER}allStatesValid(){return this.b.min===null}getStartState(e,t){return e===1?t:this.getEndState(e-1)}getFirstInvalidLine(e){const t=this.getFirstInvalidEndStateLineNumber();if(t===null)return null;const r=this.getStartState(t,e);if(!r)throw new B("Start state must be defined");return{lineNumber:t,startState:r}}},bo=class{constructor(){this.a=new oo(null)}getEndState(e){return this.a.get(e)}setEndState(e,t){const r=this.a.get(e);return r&&r.equals(t)?!1:(this.a.set(e,t),!0)}acceptChange(e,t){let r=e.length;t>0&&r>0&&(r--,t--),this.a.replace(e.startLineNumber,r,t)}acceptChanges(e){for(const t of e){const[r]=r1(t.text);this.acceptChange(new Ae(t.range.startLineNumber,t.range.endLineNumber+1),r+1)}}},mo=class{constructor(){this.a=[]}getRanges(){return this.a}get min(){return this.a.length===0?null:this.a[0].start}removeMin(){if(this.a.length===0)return null;const e=this.a[0];return e.start+1===e.endExclusive?this.a.shift():this.a[0]=new G(e.start+1,e.endExclusive),e.start}delete(e){const t=this.a.findIndex(r=>r.contains(e));if(t!==-1){const r=this.a[t];r.start===e?r.endExclusive===e+1?this.a.splice(t,1):this.a[t]=new G(e+1,r.endExclusive):r.endExclusive===e+1?this.a[t]=new G(r.start,e):this.a.splice(t,1,new G(r.start,e),new G(e+1,r.endExclusive))}}addRange(e){G.addRange(e,this.a)}addRangeAndResize(e,t){let r=0;for(;!(r>=this.a.length||e.start<=this.a[r].endExclusive);)r++;let n=r;for(;!(n>=this.a.length||e.endExclusive<this.a[n].start);)n++;const i=t-e.length;for(let o=n;o<this.a.length;o++)this.a[o]=this.a[o].delta(i);if(r===n){const o=new G(e.start,e.start+t);o.isEmpty||this.a.splice(r,0,o)}else{const o=Math.min(e.start,this.a[r].start),a=Math.max(e.endExclusive,this.a[n-1].endExclusive),l=new G(o,a+i);l.isEmpty?this.a.splice(r,n-r):this.a.splice(r,n-r,l)}}toString(){return this.a.map(e=>e.toString()).join(" + ")}},po=class extends ie{constructor(e,t,r,n,i,o,a){super(),this.c=e,this.f=t,this.g=r,this.h=n,this.j=i,this.m=o,this.n=a,this.a=[],this.b=this.B(new ee),this.onDidEncounterLanguage=this.b.event}get backgroundTokenizerShouldOnlyVerifyTokens(){return this.j()}getInitialState(){return this.f}tokenize(e,t,r){throw new Error("Not supported!")}createBackgroundTokenizer(e,t){if(this.h)return this.h(e,t)}tokenizeEncoded(e,t,r){const n=Math.random()*1e4<1,i=this.n||n,o=i?new V1(!0):void 0,a=this.c.tokenizeLine2(e,r,500);if(i){const u=o.elapsed();(n||u>32)&&this.m(u,e.length,n)}if(a.stoppedEarly)return console.warn(`Time limit reached when tokenizing line: ${e.substring(0,100)}`),new n1(a.tokens,r);if(this.g){const u=this.a,c=a.tokens;for(let f=0,h=c.length>>>1;f<h;f++){const b=c[(f<<1)+1],w=ke.getLanguageId(b);u[w]||(u[w]=!0,this.b.fire(w))}}let l;return r.equals(a.ruleStack)?l=r:l=a.ruleStack,new n1(a.tokens,l)}},vo=class extends ie{get backgroundTokenizerShouldOnlyVerifyTokens(){return this.b.backgroundTokenizerShouldOnlyVerifyTokens}constructor(e,t,r,n){super(),this.a=e,this.b=t,this.c=n,this.B(Cr(this.c)),this.B(r)}getInitialState(){return this.b.getInitialState()}tokenize(e,t,r){throw new Error("Not supported!")}tokenizeEncoded(e,t,r){return e.length>=this.c.get()?so(this.a,r):this.b.tokenizeEncoded(e,t,r)}createBackgroundTokenizer(e,t){if(this.b.createBackgroundTokenizer)return this.b.createBackgroundTokenizer(e,t)}},wo=class extends Ks{constructor(e,t,r,n,i,o,a,l){super(e,t,r,n),this.s=i,this.t=o,this.u=a,this.m=null,this.n=!1,this.o=Ps(this,-1),this.q=new Es(()=>this.w(),10),this.o.set(l,void 0),this.v()}dispose(){this.n=!0,super.dispose()}onLanguageId(e,t){this.t=e,this.u=t,this.v()}onEvents(e){super.onEvents(e),this.m?.store.acceptChanges(e.changes),this.q.schedule()}acceptMaxTokenizationLineLength(e){this.o.set(e,void 0)}retokenize(e,t){this.m&&(this.m.store.invalidateEndStateRange(new Ae(e,t)),this.q.schedule())}async v(){this.m=null;const e=this.t,t=this.u,r=await this.s.getOrCreateGrammar(e,t);if(!(this.n||e!==this.t||t!==this.u||!r)){if(r.grammar){const n=new vo(this.u,new po(r.grammar,r.initialState,!1,void 0,()=>!1,(i,o,a)=>{this.s.reportTokenizationTime(i,e,r.sourceExtensionId,o,a)},!1),ie.None,this.o);this.m=new fo(this.b.length,n)}else this.m=null;this.w()}}async w(){if(this.n||!this.m)return;if(!this.p){const{diffStateStacksRefEq:t}=await Gt("vscode-textmate","release/main.js");this.p=t}const e=new Date().getTime();for(;;){let t=0;const r=new ho,n=new yo;for(;;){const a=this.m.getFirstInvalidLine();if(a===null||t>200)break;t++;const l=this.b[a.lineNumber-1],u=this.m.tokenizationSupport.tokenizeEncoded(l,!0,a.startState);if(this.m.store.setEndState(a.lineNumber,u.endState)){const f=this.p(a.startState,u.endState);n.setState(a.lineNumber,f)}else n.setState(a.lineNumber,null);if(wt.convertToEndOffset(u.tokens,l.length),r.add(a.lineNumber,u.tokens),new Date().getTime()-e>20)break}if(t===0)break;const i=n.getStateDeltas();if(this.s.setTokensAndStates(this.d,r.serialize(),i),new Date().getTime()-e>20){E1(()=>this.w());return}}}},yo=class{constructor(){this.a=-1,this.b=[]}setState(e,t){e===this.a+1?this.b[this.b.length-1].stateDeltas.push(t):this.b.push({startLineNumber:e,stateDeltas:[t]}),this.a=e}getStateDeltas(){return this.b}},Eo=class h1{static{this.CHANNEL_NAME="textMateWorkerHost"}static getChannel(t){return t.getChannel(h1.CHANNEL_NAME)}static setChannel(t,r){t.setChannel(h1.CHANNEL_NAME,r)}};function No(e){return new Lo(e)}var Lo=class{constructor(e){this.b=new Map,this.c=[],this.d=Promise.resolve(null),this.a=Eo.getChannel(e)}async $init(e){const t=e.grammarDefinitions.map(r=>({location:J.revive(r.location),language:r.language,scopeName:r.scopeName,embeddedLanguages:r.embeddedLanguages,tokenTypes:r.tokenTypes,injectTo:r.injectTo,balancedBracketSelectors:r.balancedBracketSelectors,unbalancedBracketSelectors:r.unbalancedBracketSelectors,sourceExtensionId:r.sourceExtensionId}));this.d=this.f(t,e.onigurumaWASMUri)}async f(e,t){const r=await Gt("vscode-textmate","release/main.js"),n=await Gt("vscode-oniguruma","release/main.js"),o=await(await fetch(t)).arrayBuffer();await n.loadWASM(o);const a=Promise.resolve({createOnigScanner:l=>n.createOnigScanner(l),createOnigString:l=>n.createOnigString(l)});return new ms({logTrace:l=>{},logError:(l,u)=>console.error(l,u),readFile:l=>this.a.$readFile(l)},e,r,a)}$acceptNewModel(e){const t=J.revive(e.uri),r=this;this.b.set(e.controllerId,new wo(t,e.lines,e.EOL,e.versionId,{async getOrCreateGrammar(n,i){const o=await r.d;return o?(r.c[i]||(r.c[i]=o.createGrammar(n,i)),r.c[i]):Promise.resolve(null)},setTokensAndStates(n,i,o){r.a.$setTokensAndStates(e.controllerId,n,i,o)},reportTokenizationTime(n,i,o,a,l){r.a.$reportTokenizationTime(n,i,o,a,l)}},e.languageId,e.encodedLanguageId,e.maxTokenizationLineLength))}$acceptModelChanged(e,t){this.b.get(e).onEvents(t)}$retokenize(e,t,r){this.b.get(e).retokenize(t,r)}$acceptModelLanguageChanged(e,t,r){this.b.get(e).onLanguageId(t,r)}$acceptRemovedModel(e){const t=this.b.get(e);t&&(t.dispose(),this.b.delete(e))}async $acceptTheme(e,t){(await this.d)?.setTheme(e,t)}$acceptMaxTokenizationLineLength(e,t){this.b.get(e).acceptMaxTokenizationLineLength(t)}},s1="default",Co="$initialize",bn;(function(e){e[e.Request=0]="Request",e[e.Reply=1]="Reply",e[e.SubscribeEvent=2]="SubscribeEvent",e[e.Event=3]="Event",e[e.UnsubscribeEvent=4]="UnsubscribeEvent"})(bn||(bn={}));var Ao=class{constructor(e,t,r,n,i){this.vsWorker=e,this.req=t,this.channel=r,this.method=n,this.args=i,this.type=0}},mn=class{constructor(e,t,r,n){this.vsWorker=e,this.seq=t,this.res=r,this.err=n,this.type=1}},ko=class{constructor(e,t,r,n,i){this.vsWorker=e,this.req=t,this.channel=r,this.eventName=n,this.arg=i,this.type=2}},So=class{constructor(e,t,r){this.vsWorker=e,this.req=t,this.event=r,this.type=3}},$o=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},xo=class{constructor(e){this.a=-1,this.g=e,this.b=0,this.c=Object.create(null),this.d=new Map,this.f=new Map}setWorkerId(e){this.a=e}sendMessage(e,t,r){const n=String(++this.b);return new Promise((i,o)=>{this.c[n]={resolve:i,reject:o},this.o(new Ao(this.a,n,e,t,r))})}listen(e,t,r){let n=null;const i=new ee({onWillAddFirstListener:()=>{n=String(++this.b),this.d.set(n,i),this.o(new ko(this.a,n,e,t,r))},onDidRemoveLastListener:()=>{this.d.delete(n),this.o(new $o(this.a,n)),n=null}});return i.event}handleMessage(e){!e||!e.vsWorker||this.a!==-1&&e.vsWorker!==this.a||this.h(e)}createProxyToRemoteChannel(e,t){const r={get:(n,i)=>(typeof i=="string"&&!n[i]&&(vn(i)?n[i]=o=>this.listen(e,i,o):pn(i)?n[i]=this.listen(e,i,void 0):i.charCodeAt(0)===36&&(n[i]=async(...o)=>(await t?.(),this.sendMessage(e,i,o)))),n[i])};return new Proxy(Object.create(null),r)}h(e){switch(e.type){case 1:return this.j(e);case 0:return this.k(e);case 2:return this.l(e);case 3:return this.m(e);case 4:return this.n(e)}}j(e){if(!this.c[e.seq]){console.warn("Got reply to unknown seq");return}const t=this.c[e.seq];if(delete this.c[e.seq],e.err){let r=e.err;e.err.$isError&&(r=new Error,r.name=e.err.name,r.message=e.err.message,r.stack=e.err.stack),t.reject(r);return}t.resolve(e.res)}k(e){const t=e.req;this.g.handleMessage(e.channel,e.method,e.args).then(n=>{this.o(new mn(this.a,t,n,void 0))},n=>{n.detail instanceof Error&&(n.detail=Rt(n.detail)),this.o(new mn(this.a,t,void 0,Rt(n)))})}l(e){const t=e.req,r=this.g.handleEvent(e.channel,e.eventName,e.arg)(n=>{this.o(new So(this.a,t,n))});this.f.set(t,r)}m(e){if(!this.d.has(e.req)){console.warn("Got event for unknown req");return}this.d.get(e.req).fire(e.event)}n(e){if(!this.f.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this.f.get(e.req).dispose(),this.f.delete(e.req)}o(e){const t=[];if(e.type===0)for(let r=0;r<e.args.length;r++)e.args[r]instanceof ArrayBuffer&&t.push(e.args[r]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this.g.sendMessage(e,t)}};function pn(e){return e[0]==="o"&&e[1]==="n"&&Q1(e.charCodeAt(2))}function vn(e){return/^onDynamic/.test(e)&&Q1(e.charCodeAt(9))}var Oo=class{constructor(e,t){this.b=new Map,this.c=new Map,this.a=new xo({sendMessage:(r,n)=>{e(r,n)},handleMessage:(r,n,i)=>this.d(r,n,i),handleEvent:(r,n,i)=>this.f(r,n,i)}),this.requestHandler=t(this)}onmessage(e){this.a.handleMessage(e)}d(e,t,r){if(e===s1&&t===Co)return this.g(r[0]);const n=e===s1?this.requestHandler:this.b.get(e);if(!n)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof n[t]!="function")return Promise.reject(new Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(n[t].apply(n,r))}catch(i){return Promise.reject(i)}}f(e,t,r){const n=e===s1?this.requestHandler:this.b.get(e);if(!n)throw new Error(`Missing channel ${e} on worker thread`);if(vn(t)){const i=n[t].call(n,r);if(typeof i!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return i}if(pn(t)){const i=n[t];if(typeof i!="function")throw new Error(`Missing event ${t} on request handler.`);return i}throw new Error(`Malformed event name ${t}`)}setChannel(e,t){this.b.set(e,t)}getChannel(e){if(!this.c.has(e)){const t=this.a.createProxyToRemoteChannel(e);this.c.set(e,t)}return this.c.get(e)}async g(e){this.a.setWorkerId(e)}},o1=!1;function _o(e){if(o1)throw new Error("WebWorker already initialized!");o1=!0;const t=new Oo(r=>globalThis.postMessage(r),r=>e(r));return globalThis.onmessage=r=>{t.onmessage(r.data)},t}function To(e){globalThis.onmessage=t=>{o1||_o(e)}}To(No);

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/cb0c47c0cfaad0757385834bd89d410c78a856c0/core/vs/workbench/services/textMate/browser/backgroundTokenization/worker/textMateTokenizationWorker.workerMain.js.map
