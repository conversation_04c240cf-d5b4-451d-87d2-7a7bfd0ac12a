{"name": "native-watchdog", "version": "1.4.2", "description": "A native module that kills the current process if the event loop is unresponsive", "main": "index.js", "typings": "index.d.ts", "scripts": {"test": "node test/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/node-native-watchdog.git"}, "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/node-native-watchdog/issues"}, "homepage": "https://github.com/Microsoft/node-native-watchdog#readme"}